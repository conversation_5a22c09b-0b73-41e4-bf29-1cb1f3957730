{% extends "base.html" %}

{% block title %}عرض السجل الصحي - {{ record.cattle.tag_number }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-heartbeat"></i> تفاصيل السجل الصحي</h1>
            <div>
                <a href="{{ url_for('edit_health_record', record_id=record.id) }}" class="btn btn-warning me-2">
                    <i class="fas fa-edit"></i> تعديل
                </a>
                <a href="{{ url_for('cattle_detail', id=record.cattle.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> معلومات السجل
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong>البقرة:</strong> {{ record.cattle.tag_number }} - {{ record.cattle.name or 'بدون اسم' }}
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>نوع السجل:</strong> {{ record.record_type }}
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong>التاريخ:</strong> {{ record.date.strftime('%Y/%m/%d') if record.date else '-' }}
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>الطبيب البيطري:</strong> {{ record.veterinarian or '-' }}
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong>التكلفة:</strong> {{ "%.2f"|format(record.cost) if record.cost else '0.00' }} د.أ
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>الموعد التالي:</strong> {{ record.next_due_date.strftime('%Y/%m/%d') if record.next_due_date else '-' }}
                    </div>
                </div>
                
                <div class="mb-3">
                    <strong>الوصف:</strong>
                    <p class="mt-2">{{ record.description or '-' }}</p>
                </div>
                
                {% if record.notes %}
                <div class="mb-3">
                    <strong>ملاحظات:</strong>
                    <p class="mt-2">{{ record.notes }}</p>
                </div>
                {% endif %}
                
                <div class="text-muted small">
                    <i class="fas fa-clock"></i> تم الإنشاء: {{ record.created_at.strftime('%Y/%m/%d %H:%M') if record.created_at else '-' }}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
