{% extends "base.html" %}

{% block title %}عرض سجل الحليب - {{ record.cattle.tag_number }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-tint"></i> تفاصيل سجل إنتاج الحليب</h1>
            <div>
                <a href="{{ url_for('edit_milk_record', record_id=record.id) }}" class="btn btn-warning me-2">
                    <i class="fas fa-edit"></i> تعديل
                </a>
                <a href="{{ url_for('cattle_detail', id=record.cattle.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> معلومات الإنتاج
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong>البقرة:</strong> {{ record.cattle.tag_number }} - {{ record.cattle.name or 'بدون اسم' }}
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>التاريخ:</strong> {{ record.date.strftime('%Y/%m/%d') if record.date else '-' }}
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <strong>إنتاج الصباح:</strong> {{ record.morning_amount or 0 }} لتر
                    </div>
                    <div class="col-md-4 mb-3">
                        <strong>إنتاج المساء:</strong> {{ record.evening_amount or 0 }} لتر
                    </div>
                    <div class="col-md-4 mb-3">
                        <strong>الإجمالي:</strong> {{ record.total_amount or 0 }} لتر
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong>درجة الجودة:</strong> 
                        {% if record.quality_grade == 'ممتاز' %}
                            <span class="badge bg-success">{{ record.quality_grade }}</span>
                        {% elif record.quality_grade == 'جيد' %}
                            <span class="badge bg-primary">{{ record.quality_grade }}</span>
                        {% elif record.quality_grade == 'مقبول' %}
                            <span class="badge bg-warning">{{ record.quality_grade }}</span>
                        {% else %}
                            <span class="badge bg-secondary">غير محدد</span>
                        {% endif %}
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>القيمة المقدرة:</strong> {{ "%.2f"|format((record.total_amount or 0) * 0.8) }} د.أ
                    </div>
                </div>
                
                {% if record.notes %}
                <div class="mb-3">
                    <strong>ملاحظات:</strong>
                    <p class="mt-2">{{ record.notes }}</p>
                </div>
                {% endif %}
                
                <div class="text-muted small">
                    <i class="fas fa-clock"></i> تم الإنشاء: {{ record.created_at.strftime('%Y/%m/%d %H:%M') if record.created_at else '-' }}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
