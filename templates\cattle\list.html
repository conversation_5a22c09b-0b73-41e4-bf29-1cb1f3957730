{% extends "base.html" %}

{% block title %}قائمة الأبقار - نظام إدارة الأبقار{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-cow"></i> إدارة الأبقار</h1>
            <a href="{{ url_for('add_cattle') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة بقرة جديدة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-list"></i> قائمة الأبقار
            </div>
            <div class="card-body">
                {% if cattle %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>رقم العلامة</th>
                                <th>الاسم</th>
                                <th>السلالة</th>
                                <th>الجنس</th>
                                <th>العمر</th>
                                <th>الوزن (كغ)</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for cow in cattle %}
                            <tr>
                                <td>
                                    <strong class="text-primary">{{ cow.tag_number }}</strong>
                                </td>
                                <td>{{ cow.name or '-' }}</td>
                                <td>{{ cow.breed or '-' }}</td>
                                <td>
                                    <span class="gender-text" style="color:#000 !important; font-weight:700 !important; opacity:1 !important;">
                                        {{ cow.gender if cow.gender and cow.gender|trim != '' else 'غير محدد' }}
                                    </span>
                                </td>
                                <td>
                                    {% if cow.birth_date %}
                                        {{ cow.birth_date | calculate_age }} سنة
                                    {% else %}
                                        غير محدد
                                    {% endif %}
                                </td>
                                <td>{{ cow.weight or '-' }}</td>
                                <td>
                                    {% if cow.status == 'نشط' %}
                                        <span class="badge bg-success">{{ cow.status }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ cow.status }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('cattle_detail', id=cow.id) }}"
                                           class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('edit_cattle', id=cow.id) }}"
                                           class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-danger"
                                                onclick="confirmDelete({{ cow.id }}, '{{ cow.tag_number }}')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-cow fa-5x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد أبقار مسجلة</h4>
                    <p class="text-muted">ابدأ بإضافة أول بقرة في النظام</p>
                    <a href="{{ url_for('add_cattle') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة بقرة جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // تحديث الوقت كل ثانية
    setInterval(function() {
        document.querySelector('.navbar-text').innerHTML =
            '<i class="fas fa-calendar"></i> ' + moment().format('YYYY/MM/DD HH:mm:ss');
    }, 1000);

    // تأكيد الحذف
    function confirmDelete(cattleId, tagNumber) {
        if (confirm('هل أنت متأكد من حذف البقرة ' + tagNumber + '؟\nسيتم حذف جميع السجلات المرتبطة بها!')) {
            // إنشاء نموذج مخفي لإرسال طلب الحذف
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/cattle/delete/' + cattleId;
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
{% endblock %}
