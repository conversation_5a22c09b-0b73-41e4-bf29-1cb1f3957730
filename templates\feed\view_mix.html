{% extends "base.html" %}

{% block title %}{{ mix.name }} - تفاصيل الخلطة{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-seedling"></i> {{ mix.name }}</h1>
            <div>
                <a href="{{ url_for('edit_feed_mix', mix_id=mix.id) }}" class="btn btn-warning me-2">
                    <i class="fas fa-edit"></i> تعديل
                </a>
                <form method="POST" action="{{ url_for('delete_feed_mix', mix_id=mix.id) }}" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذه الخلطة؟\n\nسيتم حذف جميع مكوناتها أيضاً!')">
                    <button type="submit" class="btn btn-danger me-2">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </form>
                <a href="{{ url_for('feed_mixes') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- معلومات الخلطة -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> معلومات الخلطة
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>الاسم:</strong> {{ mix.name }}
                </div>
                
                <div class="mb-3">
                    <strong>الفئة المستهدفة:</strong>
                    <span class="badge bg-primary">{{ mix.target_group or 'عام' }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>الحالة:</strong>
                    {% if mix.is_active %}
                        <span class="badge bg-success">نشطة</span>
                    {% else %}
                        <span class="badge bg-secondary">غير نشطة</span>
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    <strong>الكمية اليومية:</strong>
                    {{ mix.daily_amount_per_head or 0 }} كغ/رأس
                </div>
                
                {% if mix.description %}
                <div class="mb-3">
                    <strong>الوصف:</strong>
                    <p class="text-muted">{{ mix.description }}</p>
                </div>
                {% endif %}
                
                <div class="text-muted small">
                    <i class="fas fa-clock"></i> تم الإنشاء: {{ mix.created_at.strftime('%Y/%m/%d') if mix.created_at else '-' }}
                </div>
            </div>
        </div>
    </div>
    
    <!-- القيم الغذائية -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-pie"></i> القيم الغذائية
            </div>
            <div class="card-body text-center">
                <div class="row">
                    <div class="col-12 mb-3">
                        <div class="border rounded p-3 bg-success bg-opacity-10">
                            <h6 class="text-success">البروتين الخام</h6>
                            <h3 class="text-success">{{ "%.1f"|format(mix.total_protein) if mix.total_protein else '0' }}%</h3>
                        </div>
                    </div>
                    
                    <div class="col-12 mb-3">
                        <div class="border rounded p-3 bg-info bg-opacity-10">
                            <h6 class="text-info">الطاقة الأيضية</h6>
                            <h3 class="text-info">{{ "%.1f"|format(mix.total_energy) if mix.total_energy else '0' }} MJ/kg</h3>
                        </div>
                    </div>
                    
                    <div class="col-12 mb-3">
                        <div class="border rounded p-3 bg-warning bg-opacity-10">
                            <h6 class="text-warning">التكلفة</h6>
                            <h3 class="text-warning">{{ "%.2f"|format(mix.total_cost_per_kg) if mix.total_cost_per_kg else '0' }} د.أ/كغ</h3>
                        </div>
                    </div>
                </div>
                
                <!-- تقييم الخلطة -->
                <div class="mt-3">
                    {% set protein = mix.total_protein or 0 %}
                    {% set energy = mix.total_energy or 0 %}
                    
                    {% if protein >= 12 and protein <= 18 and energy >= 10 and energy <= 13 %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> خلطة متوازنة ممتازة
                        </div>
                    {% elif protein < 12 or energy < 10 %}
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i> نقص في القيم الغذائية
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            <i class="fas fa-info-circle"></i> قيم غذائية عالية
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- مكونات الخلطة -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-list"></i> مكونات الخلطة ({{ mix.ingredients|length }})
            </div>
            <div class="card-body">
                {% if mix.ingredients %}
                    {% for mix_ingredient in mix.ingredients %}
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                        <div>
                            <strong>{{ mix_ingredient.ingredient.name }}</strong>
                            <br>
                            <small class="text-muted">{{ mix_ingredient.ingredient.category or 'غير محدد' }}</small>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold text-primary">{{ "%.1f"|format(mix_ingredient.percentage) }}%</div>
                            <small class="text-muted">{{ "%.1f"|format(mix_ingredient.weight_kg) if mix_ingredient.weight_kg else '0' }} كغ</small>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-exclamation-circle"></i>
                        <p>لا توجد مكونات في هذه الخلطة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- حساب التكلفة لكميات مختلفة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-calculator"></i> حاسبة التكلفة
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="quantity" class="form-label">الكمية المطلوبة (كغ)</label>
                        <input type="number" class="form-control" id="quantity" value="1000" onchange="calculateCost()">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">التكلفة الإجمالية</label>
                        <div class="form-control" id="total-cost-display">{{ "%.2f"|format((mix.total_cost_per_kg or 0) * 1000) }} د.أ</div>
                    </div>
                    <div class="col-md-3">
                        <label for="heads-count" class="form-label">عدد الرؤوس</label>
                        <input type="number" class="form-control" id="heads-count" value="{{ "%.0f"|format(1000 / (mix.daily_amount_per_head or 10)) }}" onchange="calculateCost()">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">مدة الكفاية (30 يوم)</label>
                        <div class="form-control" id="days-supply">30 يوم</div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-8">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>معلومات الحساب:</strong>
                            مدة الكفاية = الكمية ÷ (عدد الرؤوس × {{ mix.daily_amount_per_head or 10 }} كغ/رأس/يوم)
                        </div>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-success w-100" onclick="calculateFor30Days()">
                            <i class="fas fa-calendar-alt"></i> احسب للـ30 يوم
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    const mixCostPerKg = {{ mix.total_cost_per_kg or 0 }};
    const dailyAmountPerHead = {{ mix.daily_amount_per_head or 10 }};
    
    function calculateCost() {
        const quantity = parseFloat(document.getElementById('quantity').value) || 0;
        const headsCount = parseFloat(document.getElementById('heads-count').value) || 1;
        const totalCost = quantity * mixCostPerKg;

        // حساب مدة الكفاية بناءً على 30 يوم
        const dailyConsumption = headsCount * dailyAmountPerHead;
        const daysSupply = dailyConsumption > 0 ? Math.floor(quantity / dailyConsumption) : 0;

        // حساب الكمية المطلوبة لـ 30 يوم
        const quantityFor30Days = dailyConsumption * 30;

        document.getElementById('total-cost-display').textContent = totalCost.toFixed(2) + ' د.أ';
        document.getElementById('days-supply').textContent = daysSupply + ' يوم';

        // إضافة معلومات إضافية
        const infoText = `الاستهلاك اليومي: ${dailyConsumption.toFixed(1)} كغ | للـ30 يوم: ${quantityFor30Days.toFixed(0)} كغ`;
        document.querySelector('.alert-info').innerHTML =
            '<i class="fas fa-info-circle"></i> <strong>معلومات الحساب:</strong> ' + infoText;
    }

    function calculateFor30Days() {
        const headsCount = parseFloat(document.getElementById('heads-count').value) || 1;
        const dailyConsumption = headsCount * dailyAmountPerHead;
        const quantityFor30Days = dailyConsumption * 30;

        // تحديث الكمية
        document.getElementById('quantity').value = quantityFor30Days.toFixed(0);

        // إعادة حساب التكلفة
        calculateCost();

        // رسالة تأكيد
        const message = `تم حساب الكمية المطلوبة لـ ${headsCount} رأس لمدة 30 يوم: ${quantityFor30Days.toFixed(0)} كغ`;

        // عرض الرسالة في alert مؤقت
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show mt-2';
        alertDiv.innerHTML = `
            <i class="fas fa-check-circle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.querySelector('.card-body').appendChild(alertDiv);

        // إزالة الرسالة بعد 5 ثوان
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
    

    
    // حساب التكلفة عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        calculateCost();
    });
</script>
{% endblock %}
