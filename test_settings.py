#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import app, db, Settings, get_setting, set_setting

def test_settings():
    """اختبار نظام الإعدادات"""
    with app.app_context():
        # إنشاء الجداول
        db.create_all()
        
        print("🧪 اختبار نظام الإعدادات...")
        
        # اختبار حفظ الإعدادات
        print("1. اختبار حفظ الإعدادات...")
        set_setting('farm_name', 'مزرعة الاختبار')
        set_setting('owner_name', 'أحمد محمد')
        set_setting('milk_price', '0.40')
        set_setting('alert_days', '14')
        
        # اختبار قراءة الإعدادات
        print("2. اختبار قراءة الإعدادات...")
        farm_name = get_setting('farm_name')
        owner_name = get_setting('owner_name')
        milk_price = get_setting('milk_price')
        alert_days = get_setting('alert_days')
        
        print(f"   اسم المزرعة: {farm_name}")
        print(f"   اسم المالك: {owner_name}")
        print(f"   سعر الحليب: {milk_price} د.أ")
        print(f"   أيام التنبيه: {alert_days}")
        
        # اختبار تحديث الإعدادات
        print("3. اختبار تحديث الإعدادات...")
        set_setting('farm_name', 'مزرعة الأمل المحدثة')
        updated_name = get_setting('farm_name')
        print(f"   الاسم المحدث: {updated_name}")
        
        # عرض جميع الإعدادات
        print("4. جميع الإعدادات المحفوظة:")
        all_settings = Settings.query.all()
        for setting in all_settings:
            print(f"   {setting.key}: {setting.value}")
        
        print("✅ اختبار الإعدادات مكتمل!")

if __name__ == '__main__':
    test_settings()
