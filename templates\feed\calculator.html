{% extends "base.html" %}

{% block title %}حاسبة الخلطات العلفية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-calculator"></i> حاسبة الخلطات العلفية</h1>
            <div>
                <a href="{{ url_for('feed_ingredients') }}" class="btn btn-warning me-2">
                    <i class="fas fa-wheat-awn"></i> المواد العلفية
                </a>
                <a href="{{ url_for('feed_mixes') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للخلطات
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- منطقة بناء الخلطة -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-flask"></i> بناء الخلطة
                <div class="float-end">
                    <button type="button" class="btn btn-sm btn-success me-2" onclick="addCalculatorIngredient()">
                        <i class="fas fa-plus"></i> إضافة مكون
                    </button>
                    <a href="{{ url_for('feed_ingredients') }}" class="btn btn-sm btn-outline-primary" target="_blank">
                        <i class="fas fa-warehouse"></i> إدارة المواد
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-3">
                    <i class="fas fa-info-circle"></i>
                    <strong>ملاحظة:</strong> الحسابات مبنية على خلطة وزنها 1000 كغ
                </div>
                <div id="calculator-ingredients">
                    <!-- سيتم إضافة المكونات هنا -->
                    <div class="alert alert-info" id="no-calc-ingredients-message">
                        <i class="fas fa-info-circle"></i>
                        اضغط "إضافة مكون" لبدء حساب الخلطة
                    </div>
                </div>
                
                <div class="mt-3">
                    <button type="button" class="btn btn-info" onclick="calculateNutrition()">
                        <i class="fas fa-calculator"></i> حساب القيم الغذائية
                    </button>
                    <button type="button" class="btn btn-warning" onclick="clearCalculator()">
                        <i class="fas fa-eraser"></i> مسح الكل
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- النتائج -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-pie"></i> نتائج الخلطة
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">إجمالي النسب:</label>
                    <div class="progress mb-2">
                        <div class="progress-bar" id="percentage-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small id="percentage-text" class="text-muted">0%</small>
                </div>
                
                <div class="row text-center">
                    <div class="col-12 mb-3">
                        <div class="border rounded p-3">
                            <h6 class="text-success">البروتين الخام</h6>
                            <h4 id="result-protein">0%</h4>
                        </div>
                    </div>
                    
                    <div class="col-12 mb-3">
                        <div class="border rounded p-3">
                            <h6 class="text-info">الطاقة الأيضية</h6>
                            <h4 id="result-energy">0 MJ/kg</h4>
                        </div>
                    </div>
                    
                    <div class="col-12 mb-3">
                        <div class="border rounded p-3">
                            <h6 class="text-warning">التكلفة</h6>
                            <h4 id="result-cost">0 د.أ/كغ</h4>
                        </div>
                    </div>
                </div>
                
                <!-- توصيات -->
                <div class="mt-4">
                    <h6><i class="fas fa-lightbulb"></i> التوصيات:</h6>
                    <div id="recommendations" class="small text-muted">
                        أضف المكونات لرؤية التوصيات
                    </div>
                </div>
                
                <!-- حفظ كخلطة -->
                <div class="mt-4">
                    <button type="button" class="btn btn-primary w-100" onclick="saveAsNewMix()" id="save-mix-btn" disabled>
                        <i class="fas fa-save"></i> حفظ كخلطة جديدة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول المواد العلفية المتاحة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-database"></i> المواد العلفية المتاحة
            </div>
            <div class="card-body">
                {% if ingredients %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>المادة</th>
                                <th>الفئة</th>
                                <th>البروتين %</th>
                                <th>الطاقة MJ/kg</th>
                                <th>السعر د.أ/كغ</th>
                                <th>إضافة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for ingredient in ingredients %}
                            <tr>
                                <td>{{ ingredient.name }}</td>
                                <td><span class="badge bg-secondary">{{ ingredient.category or '-' }}</span></td>
                                <td>{{ "%.1f"|format(ingredient.protein_percentage) if ingredient.protein_percentage else '-' }}</td>
                                <td>{{ "%.1f"|format(ingredient.energy_value) if ingredient.energy_value else '-' }}</td>
                                <td>{{ "%.2f"|format(ingredient.price_per_kg) if ingredient.price_per_kg else '-' }}</td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="quickAddIngredient({{ ingredient.id }}, '{{ ingredient.name }}')">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center">
                    <p class="text-muted">لا توجد مواد علفية. أضف مواد علفية أولاً.</p>
                    <a href="{{ url_for('add_feed_ingredient') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة مادة علفية
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    let calculatorCount = 0;
    const ingredients = {{ ingredients_data|tojson }};

    console.log('المواد العلفية في الحاسبة:', ingredients);
    console.log('عدد المواد العلفية:', ingredients.length);

    function addCalculatorIngredient() {
        // إخفاء رسالة "لا توجد مكونات"
        const noIngredientsMsg = document.getElementById('no-calc-ingredients-message');
        if (noIngredientsMsg) {
            noIngredientsMsg.style.display = 'none';
        }

        calculatorCount++;
        const container = document.getElementById('calculator-ingredients');

        const html = `
            <div class="row mb-2 calculator-ingredient" id="calc-ingredient-${calculatorCount}">
                <div class="col-md-5">
                    <select class="form-select ingredient-select" onchange="calculateNutrition()">
                        <option value="">اختر المادة العلفية</option>
                        ${ingredients.map(ing => `<option value="${ing.id}" data-protein="${ing.protein_percentage || 0}" data-energy="${ing.energy_value || 0}" data-price="${ing.price_per_kg || 0}">${ing.name} (${ing.category || 'غير محدد'})</option>`).join('')}
                    </select>
                </div>
                <div class="col-md-3">
                    <input type="number" step="0.1" class="form-control percentage-input" placeholder="النسبة %" onchange="calculateNutrition()">
                </div>
                <div class="col-md-3">
                    <input type="text" class="form-control weight-display" placeholder="الوزن (كغ/1000)" readonly>
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeCalculatorIngredient(${calculatorCount})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', html);
    }

    function removeCalculatorIngredient(id) {
        document.getElementById(`calc-ingredient-${id}`).remove();
        calculateNutrition();
    }

    function calculateNutrition() {
        let totalPercentage = 0;
        let totalProtein = 0;
        let totalEnergy = 0;
        let totalCost = 0;

        const rows = document.querySelectorAll('.calculator-ingredient');

        rows.forEach(row => {
            const select = row.querySelector('.ingredient-select');
            const percentageInput = row.querySelector('.percentage-input');
            const weightDisplay = row.querySelector('.weight-display');

            const percentage = parseFloat(percentageInput.value) || 0;
            const selectedOption = select.options[select.selectedIndex];

            if (percentage > 0) {
                totalPercentage += percentage;
                weightDisplay.value = (percentage * 10) + ' كغ'; // لكل 1000 كغ

                if (selectedOption.value) {
                    const protein = parseFloat(selectedOption.dataset.protein) || 0;
                    const energy = parseFloat(selectedOption.dataset.energy) || 0;
                    const price = parseFloat(selectedOption.dataset.price) || 0;

                    totalProtein += (percentage * protein / 100);
                    totalEnergy += (percentage * energy / 100);
                    totalCost += (percentage * price / 100);
                }
            } else {
                weightDisplay.value = '';
            }
        });

        // تحديث العرض
        document.getElementById('percentage-text').textContent = totalPercentage.toFixed(1) + '%';
        document.getElementById('result-protein').textContent = totalProtein.toFixed(1) + '%';
        document.getElementById('result-energy').textContent = totalEnergy.toFixed(1) + ' MJ/kg';
        document.getElementById('result-cost').textContent = totalCost.toFixed(2) + ' د.أ/كغ';

        // تحديث شريط التقدم
        const progressBar = document.getElementById('percentage-bar');
        progressBar.style.width = Math.min(totalPercentage, 100) + '%';

        if (totalPercentage === 100) {
            progressBar.className = 'progress-bar bg-success';
        } else if (totalPercentage > 100) {
            progressBar.className = 'progress-bar bg-danger';
        } else {
            progressBar.className = 'progress-bar bg-warning';
        }

        // تحديث التوصيات
        updateRecommendations(totalProtein, totalEnergy, totalPercentage);

        // تفعيل زر الحفظ
        const saveBtn = document.getElementById('save-mix-btn');
        saveBtn.disabled = !(totalPercentage === 100 && rows.length > 0);
    }

    function updateRecommendations(protein, energy, percentage) {
        const recommendations = document.getElementById('recommendations');
        let tips = [];

        if (percentage !== 100) {
            tips.push(`⚠️ إجمالي النسب يجب أن يكون 100% (حالياً: ${percentage.toFixed(1)}%)`);
        }

        if (protein < 12) {
            tips.push('🔴 نسبة البروتين منخفضة (أقل من 12%)');
        } else if (protein > 18) {
            tips.push('🟡 نسبة البروتين عالية (أكثر من 18%)');
        } else {
            tips.push('✅ نسبة البروتين مناسبة');
        }

        if (energy < 10) {
            tips.push('🔴 الطاقة منخفضة (أقل من 10 MJ/kg)');
        } else if (energy > 13) {
            tips.push('🟡 الطاقة عالية (أكثر من 13 MJ/kg)');
        } else {
            tips.push('✅ الطاقة مناسبة');
        }

        recommendations.innerHTML = tips.join('<br>');
    }

    function quickAddIngredient(id, name) {
        addCalculatorIngredient();
        const lastRow = document.querySelector('.calculator-ingredient:last-child');
        const select = lastRow.querySelector('.ingredient-select');
        select.value = id;
        calculateNutrition();
    }

    function clearCalculator() {
        if (confirm('هل تريد مسح جميع المكونات؟')) {
            document.getElementById('calculator-ingredients').innerHTML = '';
            calculateNutrition();
        }
    }

    function saveAsNewMix() {
        alert('سيتم إضافة ميزة حفظ الخلطة من الحاسبة قريباً!');
    }

    // إضافة مكون افتراضي
    document.addEventListener('DOMContentLoaded', function() {
        console.log('تحميل حاسبة الخلطات');
        if (ingredients.length > 0) {
            addCalculatorIngredient();
            console.log('تم إضافة مكون افتراضي في الحاسبة');
        } else {
            console.log('لا توجد مواد علفية في الحاسبة!');
            document.getElementById('calculator-ingredients').innerHTML =
                '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle"></i> لا توجد مواد علفية متاحة! يرجى إضافة مواد علفية أولاً من <a href="/feed/ingredients">قائمة المواد العلفية</a>.</div>';
        }
    });
</script>
{% endblock %}
