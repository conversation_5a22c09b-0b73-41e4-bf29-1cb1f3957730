{% extends "base.html" %}

{% block title %}تقرير الأداء - نظام إدارة الأبقار{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-chart-line"></i> تقرير الأداء الشامل</h1>
            <div>
                <button class="btn btn-success me-2" onclick="exportPerformanceReport()">
                    <i class="fas fa-download"></i> تصدير التقرير
                </button>
                <button class="btn btn-primary me-2" onclick="printReport()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <a href="{{ url_for('saved_reports') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للتقارير
                </a>
            </div>
        </div>
    </div>
</div>

<!-- فترة التقرير -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-calendar"></i> فترة التقرير
            </div>
            <div class="card-body">
                <form method="GET" action="{{ url_for('performance_report') }}">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-sync"></i> تحديث التقرير
                            </button>
                        </div>
                    </div>
                </form>

                <div class="mt-3">
                    <div class="row">
                        <div class="col-md-3">
                            <button class="btn btn-outline-secondary w-100" onclick="setDateRange(7)">
                                آخر 7 أيام
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-secondary w-100" onclick="setDateRange(30)">
                                آخر 30 يوم
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-secondary w-100" onclick="setDateRange(90)">
                                آخر 3 أشهر
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-secondary w-100" onclick="setCurrentMonth()">
                                الشهر الحالي
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مؤشرات الأداء الرئيسية -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card text-white bg-primary">
            <div class="card-body text-center">
                <i class="fas fa-cow fa-2x mb-2"></i>
                <h3>{{ total_cattle }}</h3>
                <p class="mb-0">إجمالي الأبقار</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-2">
        <div class="card text-white bg-success">
            <div class="card-body text-center">
                <i class="fas fa-tint fa-2x mb-2"></i>
                <h3>{{ "%.1f"|format(total_milk_production) }}</h3>
                <p class="mb-0">إنتاج الحليب (لتر)</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-2">
        <div class="card text-white bg-info">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-2x mb-2"></i>
                <h3>{{ "%.1f"|format(avg_daily_production) }}</h3>
                <p class="mb-0">متوسط يومي (لتر)</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-2">
        <div class="card text-white bg-warning">
            <div class="card-body text-center">
                <i class="fas fa-coins fa-2x mb-2"></i>
                <h3>{{ "%.2f"|format(total_income) }}</h3>
                <p class="mb-0">إجمالي الإيرادات (د.أ)</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-2">
        <div class="card text-white bg-danger">
            <div class="card-body text-center">
                <i class="fas fa-credit-card fa-2x mb-2"></i>
                <h3>{{ "%.2f"|format(total_expenses) }}</h3>
                <p class="mb-0">إجمالي المصروفات (د.أ)</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-2">
        <div class="card text-white {% if net_profit >= 0 %}bg-success{% else %}bg-danger{% endif %}">
            <div class="card-body text-center">
                <i class="fas fa-balance-scale fa-2x mb-2"></i>
                <h3>{{ "%.2f"|format(net_profit) }}</h3>
                <p class="mb-0">صافي الربح (د.أ)</p>
            </div>
        </div>
    </div>
</div>

<!-- أداء الأبقار -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-star"></i> أداء الأبقار المنتجة
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>البقرة</th>
                                <th>إجمالي الإنتاج (لتر)</th>
                                <th>متوسط يومي (لتر)</th>
                                <th>القيمة المقدرة (د.أ)</th>
                                <th>التقييم</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for cattle_performance in cattle_performances %}
                            <tr>
                                <td>
                                    <strong>{{ cattle_performance.tag_number }}</strong>
                                    {% if cattle_performance.name %}
                                        <br><small class="text-muted">{{ cattle_performance.name }}</small>
                                    {% endif %}
                                </td>
                                <td class="text-end">{{ "%.1f"|format(cattle_performance.total_production) }}</td>
                                <td class="text-end">{{ "%.1f"|format(cattle_performance.avg_daily) }}</td>
                                <td class="text-end currency">{{ "%.2f"|format(cattle_performance.estimated_value) }}</td>
                                <td>
                                    {% if cattle_performance.avg_daily >= 15 %}
                                        <span class="badge bg-success">ممتاز</span>
                                    {% elif cattle_performance.avg_daily >= 10 %}
                                        <span class="badge bg-info">جيد جداً</span>
                                    {% elif cattle_performance.avg_daily >= 5 %}
                                        <span class="badge bg-warning">جيد</span>
                                    {% else %}
                                        <span class="badge bg-danger">ضعيف</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-secondary">
                            <tr class="fw-bold">
                                <td>الإجمالي</td>
                                <td class="text-end">{{ "%.1f"|format(total_milk_production) }} لتر</td>
                                <td class="text-end">{{ "%.1f"|format(avg_daily_production) }} لتر</td>
                                <td class="text-end currency">{{ "%.2f"|format(total_milk_value) }} د.أ</td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-pie"></i> توزيع الإنتاج
            </div>
            <div class="card-body">
                <canvas id="productionChart" width="300" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- تحليل الجودة -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-award"></i> تحليل جودة الحليب
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-3">
                        <div class="border rounded p-2 mb-2">
                            <div class="text-success fw-bold">{{ quality_stats.excellent }}</div>
                            <small>ممتاز</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="border rounded p-2 mb-2">
                            <div class="text-info fw-bold">{{ quality_stats.very_good }}</div>
                            <small>جيد جداً</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="border rounded p-2 mb-2">
                            <div class="text-primary fw-bold">{{ quality_stats.good }}</div>
                            <small>جيد</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="border rounded p-2 mb-2">
                            <div class="text-warning fw-bold">{{ quality_stats.acceptable }}</div>
                            <small>مقبول</small>
                        </div>
                    </div>
                </div>
                <canvas id="qualityChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-heartbeat"></i> الحالة الصحية
            </div>
            <div class="card-body">
                <div class="row text-center mb-3">
                    <div class="col-4">
                        <div class="border rounded p-2">
                            <div class="text-success fw-bold">{{ health_stats.healthy }}</div>
                            <small>سليمة</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border rounded p-2">
                            <div class="text-warning fw-bold">{{ health_stats.needs_attention }}</div>
                            <small>تحتاج متابعة</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border rounded p-2">
                            <div class="text-danger fw-bold">{{ health_stats.sick }}</div>
                            <small>مريضة</small>
                        </div>
                    </div>
                </div>
                
                <h6>التكاليف الطبية:</h6>
                <div class="progress mb-2">
                    <div class="progress-bar bg-danger" style="width: {{ (medical_costs / total_expenses * 100) if total_expenses > 0 else 0 }}%">
                        {{ "%.1f"|format((medical_costs / total_expenses * 100) if total_expenses > 0 else 0) }}%
                    </div>
                </div>
                <small class="text-muted">{{ "%.2f"|format(medical_costs) }} د.أ من إجمالي {{ "%.2f"|format(total_expenses) }} د.أ</small>
            </div>
        </div>
    </div>
</div>

<!-- تحليل الاتجاهات -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-trending-up"></i> اتجاهات الإنتاج والإيرادات
            </div>
            <div class="card-body">
                <canvas id="trendsChart" width="800" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- مؤشرات الكفاءة -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-calculator"></i> مؤشرات الكفاءة
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>التكلفة لكل لتر:</strong></td>
                        <td class="text-end">{{ "%.3f"|format(cost_per_liter) }} د.أ</td>
                    </tr>
                    <tr>
                        <td><strong>الربح لكل لتر:</strong></td>
                        <td class="text-end {% if profit_per_liter >= 0 %}text-success{% else %}text-danger{% endif %}">
                            {{ "%.3f"|format(profit_per_liter) }} د.أ
                        </td>
                    </tr>
                    <tr>
                        <td><strong>هامش الربح:</strong></td>
                        <td class="text-end {% if profit_margin >= 0 %}text-success{% else %}text-danger{% endif %}">
                            {{ "%.1f"|format(profit_margin) }}%
                        </td>
                    </tr>
                    <tr>
                        <td><strong>الإنتاج لكل بقرة:</strong></td>
                        <td class="text-end">{{ "%.1f"|format(production_per_cattle) }} لتر</td>
                    </tr>
                    <tr>
                        <td><strong>العائد على الاستثمار:</strong></td>
                        <td class="text-end {% if roi >= 0 %}text-success{% else %}text-danger{% endif %}">
                            {{ "%.1f"|format(roi) }}%
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-trophy"></i> أفضل الأبقار أداءً
            </div>
            <div class="card-body">
                {% for cattle in top_performers %}
                <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                    <div>
                        <strong>{{ cattle.tag_number }}</strong>
                        {% if cattle.name %}<br><small>{{ cattle.name }}</small>{% endif %}
                    </div>
                    <div class="text-end">
                        <div class="fw-bold text-success">{{ "%.1f"|format(cattle.avg_daily) }} لتر/يوم</div>
                        <small class="text-muted">{{ "%.2f"|format(cattle.estimated_value) }} د.أ</small>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-exclamation-triangle"></i> تحتاج تحسين
            </div>
            <div class="card-body">
                {% for cattle in low_performers %}
                <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                    <div>
                        <strong>{{ cattle.tag_number }}</strong>
                        {% if cattle.name %}<br><small>{{ cattle.name }}</small>{% endif %}
                    </div>
                    <div class="text-end">
                        <div class="fw-bold text-warning">{{ "%.1f"|format(cattle.avg_daily) }} لتر/يوم</div>
                        <small class="text-muted">{{ "%.2f"|format(cattle.estimated_value) }} د.أ</small>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- تحليل التكاليف -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-pie"></i> توزيع التكاليف
            </div>
            <div class="card-body">
                <canvas id="expensesChart" width="400" height="300"></canvas>
                <div class="mt-3">
                    <div class="row text-center">
                        <div class="col-3">
                            <div class="text-primary fw-bold">{{ "%.1f"|format(expense_breakdown.feed_percentage) }}%</div>
                            <small>علف</small>
                        </div>
                        <div class="col-3">
                            <div class="text-danger fw-bold">{{ "%.1f"|format(expense_breakdown.medical_percentage) }}%</div>
                            <small>طبي</small>
                        </div>
                        <div class="col-3">
                            <div class="text-warning fw-bold">{{ "%.1f"|format(expense_breakdown.maintenance_percentage) }}%</div>
                            <small>صيانة</small>
                        </div>
                        <div class="col-3">
                            <div class="text-info fw-bold">{{ "%.1f"|format(expense_breakdown.other_percentage) }}%</div>
                            <small>أخرى</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-lightbulb"></i> توصيات التحسين
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> تحليل الأداء:</h6>
                    <ul class="mb-0">
                        {% if avg_daily_production < 10 %}
                        <li>متوسط الإنتاج اليومي منخفض - راجع التغذية والرعاية</li>
                        {% endif %}
                        {% if profit_margin < 20 %}
                        <li>هامش الربح منخفض - راجع التكاليف وحسن الكفاءة</li>
                        {% endif %}
                        {% if expense_breakdown.medical_percentage > 15 %}
                        <li>التكاليف الطبية مرتفعة - ركز على الوقاية</li>
                        {% endif %}
                        {% if quality_stats.excellent < (quality_stats.excellent + quality_stats.very_good + quality_stats.good + quality_stats.acceptable) * 0.5 %}
                        <li>جودة الحليب تحتاج تحسين - راجع التغذية والنظافة</li>
                        {% endif %}
                    </ul>
                </div>
                
                <div class="alert alert-success">
                    <h6><i class="fas fa-thumbs-up"></i> نقاط القوة:</h6>
                    <ul class="mb-0">
                        {% if avg_daily_production >= 15 %}
                        <li>إنتاج ممتاز للحليب</li>
                        {% endif %}
                        {% if profit_margin >= 30 %}
                        <li>هامش ربح ممتاز</li>
                        {% endif %}
                        {% if quality_stats.excellent > (quality_stats.excellent + quality_stats.very_good + quality_stats.good + quality_stats.acceptable) * 0.6 %}
                        <li>جودة حليب عالية</li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ملخص التقرير -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <i class="fas fa-file-alt"></i> ملخص التقرير
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>الفترة المشمولة:</h6>
                        <p>من {{ start_date }} إلى {{ end_date }}</p>
                        
                        <h6>عدد الأيام:</h6>
                        <p>{{ days_count }} يوم</p>
                        
                        <h6>تاريخ إنشاء التقرير:</h6>
                        <p>{{ report_date }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>النتائج الرئيسية:</h6>
                        <ul>
                            <li>إجمالي إنتاج الحليب: <strong>{{ "%.1f"|format(total_milk_production) }} لتر</strong></li>
                            <li>متوسط الإنتاج اليومي: <strong>{{ "%.1f"|format(avg_daily_production) }} لتر</strong></li>
                            <li>صافي الربح: <strong class="{% if net_profit >= 0 %}text-success{% else %}text-danger{% endif %}">{{ "%.2f"|format(net_profit) }} د.أ</strong></li>
                            <li>هامش الربح: <strong>{{ "%.1f"|format(profit_margin) }}%</strong></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // رسم بياني لتوزيع الإنتاج
    const productionCtx = document.getElementById('productionChart').getContext('2d');
    const productionChart = new Chart(productionCtx, {
        type: 'doughnut',
        data: {
            labels: [{% for cattle in cattle_performances %}'{{ cattle.tag_number }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for cattle in cattle_performances %}{{ cattle.total_production }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                    '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // رسم بياني لجودة الحليب
    const qualityCtx = document.getElementById('qualityChart').getContext('2d');
    const qualityChart = new Chart(qualityCtx, {
        type: 'bar',
        data: {
            labels: ['ممتاز', 'جيد جداً', 'جيد', 'مقبول'],
            datasets: [{
                label: 'عدد السجلات',
                data: [{{ quality_stats.excellent }}, {{ quality_stats.very_good }}, {{ quality_stats.good }}, {{ quality_stats.acceptable }}],
                backgroundColor: ['#28a745', '#17a2b8', '#007bff', '#ffc107']
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // رسم بياني لتوزيع التكاليف
    const expensesCtx = document.getElementById('expensesChart').getContext('2d');
    const expensesChart = new Chart(expensesCtx, {
        type: 'pie',
        data: {
            labels: ['علف', 'طبي', 'صيانة', 'أخرى'],
            datasets: [{
                data: [
                    {{ expense_breakdown.feed_amount }},
                    {{ expense_breakdown.medical_amount }},
                    {{ expense_breakdown.maintenance_amount }},
                    {{ expense_breakdown.other_amount }}
                ],
                backgroundColor: ['#007bff', '#dc3545', '#ffc107', '#17a2b8']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // رسم بياني للاتجاهات
    const trendsCtx = document.getElementById('trendsChart').getContext('2d');
    const trendsChart = new Chart(trendsCtx, {
        type: 'line',
        data: {
            labels: [{% for trend in trends_data %}'{{ trend.date }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                label: 'إنتاج الحليب (لتر)',
                data: [{% for trend in trends_data %}{{ trend.milk_production }}{% if not loop.last %},{% endif %}{% endfor %}],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                yAxisID: 'y'
            }, {
                label: 'الإيرادات (د.أ)',
                data: [{% for trend in trends_data %}{{ trend.income }}{% if not loop.last %},{% endif %}{% endfor %}],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'التاريخ'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'إنتاج الحليب (لتر)'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'الإيرادات (د.أ)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });

    // وظائف التحكم بالتاريخ
    function setDateRange(days) {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(endDate.getDate() - days);

        document.getElementById('start_date').value = formatDate(startDate);
        document.getElementById('end_date').value = formatDate(endDate);

        // تحديث التقرير تلقائياً
        setTimeout(updateReport, 100);
    }

    function setCurrentMonth() {
        const now = new Date();
        const startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);

        document.getElementById('start_date').value = formatDate(startDate);
        document.getElementById('end_date').value = formatDate(endDate);

        // تحديث التقرير تلقائياً
        setTimeout(updateReport, 100);
    }

    function formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    function updateReport() {
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;

        if (startDate && endDate) {
            if (startDate > endDate) {
                alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
                return;
            }

            window.location.href = `{{ url_for('performance_report') }}?start_date=${startDate}&end_date=${endDate}`;
        } else {
            alert('يرجى تحديد تاريخ البداية والنهاية');
        }
    }

    function printReport() {
        window.print();
    }

    function exportPerformanceReport() {
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;
        window.location.href = `{{ url_for('export_performance_report') }}?start_date=${startDate}&end_date=${endDate}`;
    }
</script>
{% endblock %}
