{% extends "base.html" %}

{% block title %}تعديل الخلطة - {{ mix.name }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-edit"></i> تعديل الخلطة: {{ mix.name }}</h1>
            <a href="{{ url_for('view_feed_mix', mix_id=mix.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> العودة للتفاصيل
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-seedling"></i> تعديل بيانات الخلطة العلفية
            </div>
            <div class="card-body">
                <form method="POST" id="editMixForm">
                    <!-- معلومات أساسية -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم الخلطة *</label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ mix.name }}" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="target_group" class="form-label">الفئة المستهدفة</label>
                            <select class="form-select" id="target_group" name="target_group">
                                <option value="">اختر الفئة</option>
                                <option value="أبقار حلوب" {{ 'selected' if mix.target_group == 'أبقار حلوب' }}>أبقار حلوب</option>
                                <option value="أبقار جافة" {{ 'selected' if mix.target_group == 'أبقار جافة' }}>أبقار جافة</option>
                                <option value="عجول رضيعة" {{ 'selected' if mix.target_group == 'عجول رضيعة' }}>عجول رضيعة</option>
                                <option value="عجول فطام" {{ 'selected' if mix.target_group == 'عجول فطام' }}>عجول فطام</option>
                                <option value="ثيران" {{ 'selected' if mix.target_group == 'ثيران' }}>ثيران</option>
                                <option value="عام" {{ 'selected' if mix.target_group == 'عام' }}>عام</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="daily_amount_per_head" class="form-label">الكمية اليومية/رأس (كغ)</label>
                            <input type="number" step="0.1" class="form-control" id="daily_amount_per_head" name="daily_amount_per_head" value="{{ mix.daily_amount_per_head or '' }}">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف الخلطة</label>
                        <textarea class="form-control" id="description" name="description" rows="2">{{ mix.description or '' }}</textarea>
                    </div>
                    
                    <!-- مكونات الخلطة -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <i class="fas fa-list"></i> مكونات الخلطة
                            <button type="button" class="btn btn-sm btn-success float-end" onclick="addIngredient()">
                                <i class="fas fa-plus"></i> إضافة مكون
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="ingredients-container">
                                <!-- سيتم تحميل المكونات الحالية هنا -->
                            </div>
                            
                            <!-- ملخص الخلطة -->
                            <div class="alert alert-info mb-3">
                                <i class="fas fa-info-circle"></i>
                                <strong>ملاحظة:</strong> الحسابات مبنية على خلطة وزنها 1000 كغ
                            </div>
                            <div class="row mt-4 p-3 bg-light rounded">
                                <div class="col-md-3">
                                    <strong>إجمالي النسب:</strong>
                                    <div id="total-percentage" class="text-primary">0%</div>
                                </div>
                                <div class="col-md-3">
                                    <strong>إجمالي البروتين:</strong>
                                    <div id="total-protein" class="text-success">0%</div>
                                </div>
                                <div class="col-md-3">
                                    <strong>إجمالي الطاقة:</strong>
                                    <div id="total-energy" class="text-info">0 MJ/kg</div>
                                </div>
                                <div class="col-md-3">
                                    <strong>التكلفة الإجمالية:</strong>
                                    <div id="total-cost" class="text-warning">0 د.أ/كغ</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <a href="{{ url_for('view_feed_mix', mix_id=mix.id) }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary" id="save-btn">
                            <i class="fas fa-save"></i> حفظ التعديلات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    let ingredientCount = 0;
    const ingredients = {{ ingredients_data|tojson }};
    // بيانات الخلطة الحالية متاحة عبر Jinja2 loops
    
    function addIngredient(selectedId = '', selectedPercentage = '') {
        ingredientCount++;
        const container = document.getElementById('ingredients-container');
        
        const ingredientHtml = `
            <div class="row mb-3 ingredient-row" id="ingredient-${ingredientCount}">
                <div class="col-md-5">
                    <select class="form-select ingredient-select" name="ingredient_id" onchange="updateCalculations()">
                        <option value="">اختر المادة العلفية</option>
                        ${ingredients.map(ing => `<option value="${ing.id}" data-protein="${ing.protein_percentage || 0}" data-energy="${ing.energy_value || 0}" data-price="${ing.price_per_kg || 0}" ${ing.id == selectedId ? 'selected' : ''}>${ing.name}</option>`).join('')}
                    </select>
                </div>
                <div class="col-md-3">
                    <input type="number" step="0.1" class="form-control percentage-input" name="percentage" placeholder="النسبة %" value="${selectedPercentage}" onchange="updateCalculations()">
                </div>
                <div class="col-md-3">
                    <input type="text" class="form-control weight-display" placeholder="الوزن (كغ/1000)" readonly>
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeIngredient(${ingredientCount})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', ingredientHtml);
        updateCalculations();
    }
    
    function removeIngredient(id) {
        document.getElementById(`ingredient-${id}`).remove();
        updateCalculations();
    }
    
    function updateCalculations() {
        let totalPercentage = 0;
        let totalProtein = 0;
        let totalEnergy = 0;
        let totalCost = 0;
        
        const rows = document.querySelectorAll('.ingredient-row');
        
        rows.forEach(row => {
            const select = row.querySelector('.ingredient-select');
            const percentageInput = row.querySelector('.percentage-input');
            const weightDisplay = row.querySelector('.weight-display');
            
            const percentage = parseFloat(percentageInput.value) || 0;
            const selectedOption = select.options[select.selectedIndex];
            
            if (percentage > 0) {
                totalPercentage += percentage;
                weightDisplay.value = (percentage * 10) + ' كغ'; // لكل 1000 كغ
                
                if (selectedOption.value) {
                    const protein = parseFloat(selectedOption.dataset.protein) || 0;
                    const energy = parseFloat(selectedOption.dataset.energy) || 0;
                    const price = parseFloat(selectedOption.dataset.price) || 0;
                    
                    totalProtein += (percentage * protein / 100);
                    totalEnergy += (percentage * energy / 1000);
                    totalCost += (percentage * price / 1000);
                }
            } else {
                weightDisplay.value = '';
            }
        });
        
        // تحديث العرض
        document.getElementById('total-percentage').textContent = totalPercentage.toFixed(1) + '%';
        document.getElementById('total-protein').textContent = totalProtein.toFixed(1) + '%';
        document.getElementById('total-energy').textContent = totalEnergy.toFixed(1) + ' MJ/kg';
        document.getElementById('total-cost').textContent = totalCost.toFixed(2) + ' د.أ/كغ';
        
        // تفعيل/إلغاء زر الحفظ
        const saveBtn = document.getElementById('save-btn');
        if (totalPercentage === 100 && rows.length > 0) {
            saveBtn.disabled = false;
            saveBtn.classList.remove('btn-secondary');
            saveBtn.classList.add('btn-primary');
        } else {
            saveBtn.disabled = false; // السماح بالحفظ حتى لو لم تكن النسب 100%
        }
        
        // تلوين النسبة الإجمالية
        const percentageElement = document.getElementById('total-percentage');
        if (totalPercentage === 100) {
            percentageElement.className = 'text-success';
        } else if (totalPercentage > 100) {
            percentageElement.className = 'text-danger';
        } else {
            percentageElement.className = 'text-warning';
        }
    }
    
    // تحميل المكونات الحالية عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        console.log('تحميل صفحة تعديل الخلطة');
        console.log('المواد العلفية المتاحة:', ingredients.length);

        // إضافة المكونات الحالية
        {% for mix_ingredient in mix.ingredients %}
        addIngredient('{{ mix_ingredient.ingredient_id }}', '{{ mix_ingredient.percentage }}');
        console.log('تم تحميل مكون: {{ mix_ingredient.ingredient.name }}');
        {% endfor %}

        // إذا لم تكن هناك مكونات، أضف مكون فارغ
        if ({{ mix.ingredients|length }} === 0) {
            addIngredient();
        }

        updateCalculations();
    });
</script>
{% endblock %}
