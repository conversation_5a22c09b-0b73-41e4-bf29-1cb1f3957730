{% extends "base.html" %}

{% block title %}إضافة معاملة مالية - نظام إدارة الأبقار{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-plus"></i> إضافة معاملة مالية جديدة</h1>
            <a href="{{ url_for('financial_reports') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> العودة للتقارير
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-money-bill"></i> بيانات المعاملة المالية
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="transaction_type" class="form-label">نوع المعاملة *</label>
                            <select class="form-select" id="transaction_type" name="transaction_type" required>
                                <option value="">اختر نوع المعاملة</option>
                                <option value="دخل">دخل</option>
                                <option value="مصروف">مصروف</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="date" class="form-label">التاريخ *</label>
                            <input type="date" class="form-control" id="date" name="date" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label">الفئة</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">اختر الفئة</option>
                                <!-- فئات الدخل -->
                                <optgroup label="الدخل" id="income-categories" style="display: none;">
                                    <option value="بيع حليب">بيع حليب</option>
                                    <option value="بيع أبقار">بيع أبقار</option>
                                    <option value="بيع عجول">بيع عجول</option>
                                    <option value="منتجات ألبان">منتجات ألبان</option>
                                    <option value="دخل آخر">دخل آخر</option>
                                </optgroup>
                                <!-- فئات المصروفات -->
                                <optgroup label="المصروفات" id="expense-categories" style="display: none;">
                                    <option value="علف">علف</option>
                                    <option value="طبي">طبي</option>
                                    <option value="تطعيمات">تطعيمات</option>
                                    <option value="كهرباء">كهرباء</option>
                                    <option value="مياه">مياه</option>
                                    <option value="صيانة">صيانة</option>
                                    <option value="عمالة">عمالة</option>
                                    <option value="نقل">نقل</option>
                                    <option value="مصروف آخر">مصروف آخر</option>
                                </optgroup>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="amount" class="form-label">المبلغ (دينار أردني) *</label>
                            <input type="number" step="0.01" class="form-control" id="amount" name="amount" required min="0">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف *</label>
                        <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="cattle_id" class="form-label">البقرة المرتبطة (اختياري)</label>
                        <select class="form-select" id="cattle_id" name="cattle_id">
                            <option value="">غير مرتبط ببقرة محددة</option>
                            {% for cow in cattle_list %}
                                <option value="{{ cow.id }}">{{ cow.tag_number }} - {{ cow.name or 'بدون اسم' }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('financial_reports') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ المعاملة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // تعيين التاريخ الحالي كافتراضي
    document.getElementById('date').value = moment().format('YYYY-MM-DD');
    
    // تغيير الفئات حسب نوع المعاملة
    document.getElementById('transaction_type').addEventListener('change', function() {
        const incomeCategories = document.getElementById('income-categories');
        const expenseCategories = document.getElementById('expense-categories');
        const categorySelect = document.getElementById('category');
        
        // إخفاء جميع الفئات
        incomeCategories.style.display = 'none';
        expenseCategories.style.display = 'none';
        categorySelect.value = '';
        
        // عرض الفئات المناسبة
        if (this.value === 'دخل') {
            incomeCategories.style.display = 'block';
        } else if (this.value === 'مصروف') {
            expenseCategories.style.display = 'block';
        }
    });
</script>
{% endblock %}
