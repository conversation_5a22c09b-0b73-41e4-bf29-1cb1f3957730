{% extends "base.html" %}

{% block title %}تفاصيل المعاملة المالية #{{ transaction.id }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-receipt"></i> تفاصيل المعاملة #{{ transaction.id }}</h1>
            <div>
                <a href="{{ url_for('edit_financial_transaction', transaction_id=transaction.id) }}" class="btn btn-warning me-2">
                    <i class="fas fa-edit"></i> تعديل
                </a>
                <form method="POST" action="{{ url_for('delete_financial_transaction', transaction_id=transaction.id) }}" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذه المعاملة المالية؟')">
                    <button type="submit" class="btn btn-danger me-2">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </form>
                <a href="{{ url_for('financial_reports') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للتقارير
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- معلومات المعاملة -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> معلومات المعاملة
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>نوع المعاملة:</strong></div>
                    <div class="col-sm-8">
                        {% if transaction.transaction_type == 'دخل' %}
                            <span class="badge bg-success fs-6">{{ transaction.transaction_type }}</span>
                        {% else %}
                            <span class="badge bg-danger fs-6">{{ transaction.transaction_type }}</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>الفئة:</strong></div>
                    <div class="col-sm-8">
                        <span class="badge bg-secondary">{{ transaction.category or 'غير محدد' }}</span>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>المبلغ:</strong></div>
                    <div class="col-sm-8">
                        <h4 class="{% if transaction.transaction_type == 'دخل' %}text-success{% else %}text-danger{% endif %}">
                            {{ "%.2f"|format(transaction.amount) }} د.أ
                        </h4>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>التاريخ:</strong></div>
                    <div class="col-sm-8">{{ transaction.date.strftime('%Y/%m/%d') if transaction.date else 'غير محدد' }}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>الوصف:</strong></div>
                    <div class="col-sm-8">{{ transaction.description }}</div>
                </div>
                
                {% if transaction.cattle_id %}
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>البقرة المرتبطة:</strong></div>
                    <div class="col-sm-8">
                        <a href="{{ url_for('cattle_detail', id=transaction.cattle_id) }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-cow"></i> {{ transaction.cattle.tag_number }} - {{ transaction.cattle.name }}
                        </a>
                    </div>
                </div>
                {% endif %}
                
                {% if transaction.notes %}
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>ملاحظات:</strong></div>
                    <div class="col-sm-8">
                        <div class="border rounded p-2 bg-light">
                            {{ transaction.notes }}
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <div class="row">
                    <div class="col-sm-4"><strong>تاريخ الإنشاء:</strong></div>
                    <div class="col-sm-8 text-muted">
                        {{ transaction.created_at.strftime('%Y/%m/%d %H:%M') if transaction.created_at else 'غير محدد' }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- إحصائيات ذات صلة -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-bar"></i> إحصائيات ذات صلة
            </div>
            <div class="card-body">
                {% if transaction.cattle_id %}
                    <h6>إحصائيات البقرة {{ transaction.cattle.tag_number }}:</h6>
                    
                    {% set cattle_transactions = transaction.cattle.financial_records %}
                    {% set cattle_income = cattle_transactions|selectattr('transaction_type', 'equalto', 'دخل')|map(attribute='amount')|sum %}
                    {% set cattle_expenses = cattle_transactions|selectattr('transaction_type', 'equalto', 'مصروف')|map(attribute='amount')|sum %}
                    
                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <div class="border rounded p-2">
                                <small class="text-muted">إجمالي الإيرادات</small>
                                <div class="fw-bold text-success">{{ "%.2f"|format(cattle_income) }} د.أ</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-2">
                                <small class="text-muted">إجمالي المصروفات</small>
                                <div class="fw-bold text-danger">{{ "%.2f"|format(cattle_expenses) }} د.أ</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center">
                        <div class="border rounded p-2 bg-light">
                            <small class="text-muted">صافي الربح</small>
                            <div class="fw-bold {% if cattle_income - cattle_expenses >= 0 %}text-success{% else %}text-danger{% endif %}">
                                {{ "%.2f"|format(cattle_income - cattle_expenses) }} د.أ
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <small class="text-muted">عدد المعاملات: {{ cattle_transactions|length }}</small>
                    </div>
                {% else %}
                    <h6>إحصائيات الفئة "{{ transaction.category }}":</h6>
                    
                    {% set category_transactions = [] %}
                    <!-- سيتم حساب إحصائيات الفئة هنا -->
                    
                    <div class="text-center py-4">
                        <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                        <p class="text-muted">إحصائيات الفئة قيد التطوير</p>
                    </div>
                {% endif %}
            </div>
        </div>
        
        <!-- معاملات مشابهة -->
        <div class="card mt-4">
            <div class="card-header">
                <i class="fas fa-history"></i> معاملات مشابهة حديثة
            </div>
            <div class="card-body">
                {% set similar_transactions = [] %}
                <!-- سيتم جلب المعاملات المشابهة هنا -->
                
                <div class="text-center py-3">
                    <i class="fas fa-search fa-2x text-muted mb-2"></i>
                    <p class="text-muted small">المعاملات المشابهة قيد التطوير</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إجراءات سريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt"></i> إجراءات سريعة
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="{{ url_for('add_financial_transaction') }}" class="btn btn-primary w-100">
                            <i class="fas fa-plus"></i> إضافة معاملة جديدة
                        </a>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-info w-100" onclick="duplicateTransaction()">
                            <i class="fas fa-copy"></i> نسخ هذه المعاملة
                        </button>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('monthly_report') }}" class="btn btn-success w-100">
                            <i class="fas fa-chart-line"></i> التقرير الشهري
                        </a>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-warning w-100" onclick="printTransaction()">
                            <i class="fas fa-print"></i> طباعة المعاملة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function duplicateTransaction() {
        if (confirm('هل تريد إنشاء معاملة جديدة بنفس البيانات؟')) {
            // سيتم إضافة هذه الميزة لاحقاً
            alert('ميزة نسخ المعاملة قيد التطوير!');
        }
    }
    
    function printTransaction() {
        window.print();
    }
</script>
{% endblock %}
