{% extends "base.html" %}

{% block title %}الخلطات العلفية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-seedling"></i> الخلطات العلفية</h1>
            <div>
                <a href="{{ url_for('feed_calculator') }}" class="btn btn-info me-2">
                    <i class="fas fa-calculator"></i> حاسبة الخلطات
                </a>
                <a href="{{ url_for('feed_ingredients') }}" class="btn btn-warning me-2">
                    <i class="fas fa-wheat-awn"></i> المواد العلفية
                </a>
                <a href="{{ url_for('add_feed_mix') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إنشاء خلطة جديدة
                </a>
            </div>
        </div>
    </div>
</div>

{% if mixes %}
<div class="row">
    {% for mix in mixes %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100 {{ 'border-success' if mix.is_active else 'border-secondary' }}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-seedling"></i> {{ mix.name }}
                </h6>
                {% if mix.is_active %}
                    <span class="badge bg-success">نشطة</span>
                {% else %}
                    <span class="badge bg-secondary">غير نشطة</span>
                {% endif %}
            </div>
            <div class="card-body">
                <p class="text-muted small">{{ mix.description or 'لا يوجد وصف' }}</p>
                
                <div class="row text-center mb-3">
                    <div class="col-4">
                        <div class="border rounded p-2">
                            <small class="text-muted">البروتين</small>
                            <div class="fw-bold text-success">{{ "%.1f"|format(mix.total_protein) if mix.total_protein else '0' }}%</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border rounded p-2">
                            <small class="text-muted">الطاقة</small>
                            <div class="fw-bold text-info">{{ "%.1f"|format(mix.total_energy) if mix.total_energy else '0' }} MJ</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border rounded p-2">
                            <small class="text-muted">التكلفة</small>
                            <div class="fw-bold text-warning">{{ "%.2f"|format(mix.total_cost_per_kg) if mix.total_cost_per_kg else '0' }} د.أ</div>
                        </div>
                    </div>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted">الفئة المستهدفة:</small>
                    <span class="badge bg-primary">{{ mix.target_group or 'عام' }}</span>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">الكمية اليومية:</small>
                    <strong>{{ mix.daily_amount_per_head or 0 }} كغ/رأس</strong>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted">عدد المكونات:</small>
                    <strong>{{ mix.ingredients|length }}</strong>
                </div>
            </div>
            <div class="card-footer">
                <div class="btn-group w-100" role="group">
                    <a href="{{ url_for('view_feed_mix', mix_id=mix.id) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye"></i> عرض
                    </a>
                    <a href="{{ url_for('edit_feed_mix', mix_id=mix.id) }}" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-edit"></i> تعديل
                    </a>
                    <form method="POST" action="{{ url_for('delete_feed_mix', mix_id=mix.id) }}" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذه الخلطة؟\n\nسيتم حذف جميع مكوناتها أيضاً!')">
                        <button type="submit" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-seedling fa-3x text-muted mb-3"></i>
                <h4>لا توجد خلطات علفية</h4>
                <p class="text-muted">ابدأ بإنشاء خلطات علفية مخصصة لأبقارك</p>
                <div class="d-grid gap-2 d-md-block">
                    <a href="{{ url_for('feed_ingredients') }}" class="btn btn-warning">
                        <i class="fas fa-wheat-awn"></i> إضافة مواد علفية أولاً
                    </a>
                    <a href="{{ url_for('add_feed_mix') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إنشاء أول خلطة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
