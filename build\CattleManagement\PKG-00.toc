('C:\\Users\\<USER>\\Desktop\\New folder '
 '(9)\\build\\CattleManagement\\CattleManagement.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\build\\CattleManagement\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\build\\CattleManagement\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\build\\CattleManagement\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\build\\CattleManagement\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\build\\CattleManagement\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\build\\CattleManagement\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('run_app',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\run_app.py',
   'PYSOURCE'),
  ('python312.dll', 'C:\\Program Files\\Python312\\python312.dll', 'BINARY'),
  ('_decimal.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'C:\\Program Files\\Python312\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Program Files\\Python312\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Program Files\\Python312\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'C:\\Program Files\\Python312\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'C:\\Program Files\\Python312\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Program Files\\Python312\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Program Files\\Python312\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd', 'C:\\Program Files\\Python312\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_uuid.pyd', 'C:\\Program Files\\Python312\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('sqlalchemy\\cyextension\\resultproxy.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\sqlalchemy\\cyextension\\resultproxy.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\util.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\sqlalchemy\\cyextension\\util.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\processors.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\sqlalchemy\\cyextension\\processors.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\immutabledict.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\sqlalchemy\\cyextension\\immutabledict.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\collections.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\sqlalchemy\\cyextension\\collections.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\greenlet\\_greenlet.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Program Files\\Python312\\VCRUNTIME140.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Program Files\\Python312\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Program Files\\Python312\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Program Files\\Python312\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Program Files\\Python312\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('sqlite3.dll', 'C:\\Program Files\\Python312\\DLLs\\sqlite3.dll', 'BINARY'),
  ('config.py',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\config.py',
   'DATA'),
  ('static\\app.js',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\static\\app.js',
   'DATA'),
  ('static\\favicon.ico',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\static\\favicon.ico',
   'DATA'),
  ('static\\style.css',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\static\\style.css',
   'DATA'),
  ('templates\\base.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\base.html',
   'DATA'),
  ('templates\\breeding\\add.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\breeding\\add.html',
   'DATA'),
  ('templates\\breeding\\list.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\breeding\\list.html',
   'DATA'),
  ('templates\\breeding\\select_cattle.html',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\templates\\breeding\\select_cattle.html',
   'DATA'),
  ('templates\\cattle\\add.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\cattle\\add.html',
   'DATA'),
  ('templates\\cattle\\detail.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\cattle\\detail.html',
   'DATA'),
  ('templates\\cattle\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\cattle\\edit.html',
   'DATA'),
  ('templates\\cattle\\list.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\cattle\\list.html',
   'DATA'),
  ('templates\\feed\\add_ingredient.html',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\templates\\feed\\add_ingredient.html',
   'DATA'),
  ('templates\\feed\\add_mix.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\feed\\add_mix.html',
   'DATA'),
  ('templates\\feed\\calculator.html',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\templates\\feed\\calculator.html',
   'DATA'),
  ('templates\\feed\\edit_ingredient.html',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\templates\\feed\\edit_ingredient.html',
   'DATA'),
  ('templates\\feed\\edit_mix.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\feed\\edit_mix.html',
   'DATA'),
  ('templates\\feed\\ingredients.html',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\templates\\feed\\ingredients.html',
   'DATA'),
  ('templates\\feed\\mixes.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\feed\\mixes.html',
   'DATA'),
  ('templates\\feed\\view_mix.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\feed\\view_mix.html',
   'DATA'),
  ('templates\\financial\\add_transaction.html',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\templates\\financial\\add_transaction.html',
   'DATA'),
  ('templates\\financial\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\templates\\financial\\edit.html',
   'DATA'),
  ('templates\\financial\\reports.html',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\templates\\financial\\reports.html',
   'DATA'),
  ('templates\\financial\\view.html',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\templates\\financial\\view.html',
   'DATA'),
  ('templates\\health\\add.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\health\\add.html',
   'DATA'),
  ('templates\\health\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\health\\edit.html',
   'DATA'),
  ('templates\\health\\list.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\health\\list.html',
   'DATA'),
  ('templates\\health\\select_cattle.html',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\templates\\health\\select_cattle.html',
   'DATA'),
  ('templates\\health\\view.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\health\\view.html',
   'DATA'),
  ('templates\\index.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\index.html',
   'DATA'),
  ('templates\\milk\\add.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\milk\\add.html',
   'DATA'),
  ('templates\\milk\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\milk\\edit.html',
   'DATA'),
  ('templates\\milk\\list.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\milk\\list.html',
   'DATA'),
  ('templates\\milk\\select_cattle.html',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\templates\\milk\\select_cattle.html',
   'DATA'),
  ('templates\\milk\\view.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\milk\\view.html',
   'DATA'),
  ('templates\\reports\\dashboard.html',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\templates\\reports\\dashboard.html',
   'DATA'),
  ('templates\\reports\\monthly.html',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\templates\\reports\\monthly.html',
   'DATA'),
  ('templates\\reports\\saved.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\reports\\saved.html',
   'DATA'),
  ('templates\\search\\results.html',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\templates\\search\\results.html',
   'DATA'),
  ('templates\\settings.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\settings.html',
   'DATA'),
  ('templates\\settings\\index.html',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\templates\\settings\\index.html',
   'DATA'),
  ('templates\\settings\\restore.html',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\templates\\settings\\restore.html',
   'DATA'),
  ('templates\\test_links.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (9)\\templates\\test_links.html',
   'DATA'),
  ('utils.py', 'C:\\Users\\<USER>\\Desktop\\New folder (9)\\utils.py', 'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug-2.3.7.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug-2.3.7.dist-info\\RECORD',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug-2.3.7.dist-info\\REQUESTED',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug-2.3.7.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug-2.3.7.dist-info\\METADATA',
   'DATA'),
  ('flask-2.3.3.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask-2.3.3.dist-info\\RECORD',
   'DATA'),
  ('flask-2.3.3.dist-info\\LICENSE.rst',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask-2.3.3.dist-info\\LICENSE.rst',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\LICENSE.rst',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug-2.3.7.dist-info\\LICENSE.rst',
   'DATA'),
  ('flask-2.3.3.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask-2.3.3.dist-info\\entry_points.txt',
   'DATA'),
  ('flask-2.3.3.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask-2.3.3.dist-info\\REQUESTED',
   'DATA'),
  ('flask-2.3.3.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask-2.3.3.dist-info\\WHEEL',
   'DATA'),
  ('flask-2.3.3.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask-2.3.3.dist-info\\METADATA',
   'DATA'),
  ('flask-2.3.3.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask-2.3.3.dist-info\\INSTALLER',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(9)\\build\\CattleManagement\\base_library.zip',
   'DATA')],
 'python312.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
