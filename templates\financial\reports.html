{% extends "base.html" %}

{% block title %}التقارير المالية - نظام إدارة الأبقار{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-chart-line"></i> التقارير المالية</h1>
            <div>
                <a href="{{ url_for('monthly_report') }}" class="btn btn-info me-2">
                    <i class="fas fa-calendar-alt"></i> التقرير الشهري
                </a>
                <a href="{{ url_for('saved_reports') }}" class="btn btn-warning me-2">
                    <i class="fas fa-archive"></i> التقارير المحفوظة
                </a>
                <a href="{{ url_for('add_financial_transaction') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة معاملة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- ملخص مالي شهري -->
<div class="row">
    <div class="col-md-4">
        <div class="card text-white bg-success">
            <div class="card-header">
                <i class="fas fa-arrow-up"></i> الإيرادات الشهرية
            </div>
            <div class="card-body">
                <h2 class="card-title">{{ "%.2f"|format(monthly_income) }}</h2>
                <p class="card-text">دينار أردني</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card text-white bg-danger">
            <div class="card-header">
                <i class="fas fa-arrow-down"></i> المصروفات الشهرية
            </div>
            <div class="card-body">
                <h2 class="card-title">{{ "%.2f"|format(monthly_expenses) }}</h2>
                <p class="card-text">دينار أردني</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card text-white {% if net_profit >= 0 %}bg-primary{% else %}bg-warning{% endif %}">
            <div class="card-header">
                <i class="fas fa-balance-scale"></i> صافي الربح
            </div>
            <div class="card-body">
                <h2 class="card-title">{{ "%.2f"|format(net_profit) }}</h2>
                <p class="card-text">دينار أردني</p>
            </div>
        </div>
    </div>
</div>

<!-- إعدادات سريعة -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <i class="fas fa-tint"></i> سعر الحليب الحالي
            </div>
            <div class="card-body text-center">
                <h3 class="text-info milk-price-display">{{ "%.2f"|format(milk_price) }} د.أ/لتر</h3>
                <small class="text-muted">آخر تحديث: {{ milk_price_updated or 'غير محدد' }}</small>
                <div class="mt-2">
                    <button class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#milkPriceModal">
                        <i class="fas fa-edit"></i> تحديث السعر
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-calculator"></i> حاسبة الإيرادات السريعة
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">كمية الحليب (لتر)</label>
                        <input type="number" class="form-control" id="milk_quantity" value="100" onchange="calculateRevenue()">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">السعر المستخدم</label>
                        <input type="text" class="form-control" id="price_used" value="{{ "%.2f"|format(milk_price) }} د.أ/لتر" readonly>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">الإيرادات المتوقعة</label>
                        <input type="text" class="form-control fw-bold text-success" id="expected_revenue" readonly>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إجراءات سريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-plus"></i> إضافة معاملة جديدة
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="{{ url_for('add_financial_transaction') }}" class="btn btn-success w-100">
                            <i class="fas fa-plus"></i><br>
                            إضافة دخل
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('add_financial_transaction') }}" class="btn btn-danger w-100">
                            <i class="fas fa-minus"></i><br>
                            إضافة مصروف
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('monthly_report') }}" class="btn btn-info w-100">
                            <i class="fas fa-file-alt"></i><br>
                            تقرير شهري
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('monthly_report') }}" class="btn btn-warning w-100">
                            <i class="fas fa-chart-pie"></i><br>
                            تحليل مفصل
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- آخر المعاملات -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-history"></i> آخر المعاملات المالية
            </div>
            <div class="card-body">
                {% if recent_transactions %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>الفئة</th>
                                <th>الوصف</th>
                                <th>المبلغ</th>
                                <th>البقرة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in recent_transactions %}
                            <tr>
                                <td>{{ transaction.date.strftime('%Y/%m/%d') if transaction.date else '-' }}</td>
                                <td>
                                    {% if transaction.transaction_type == 'دخل' %}
                                        <span class="badge bg-success">{{ transaction.transaction_type }}</span>
                                    {% else %}
                                        <span class="badge bg-danger">{{ transaction.transaction_type }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ transaction.category or '-' }}</td>
                                <td>{{ transaction.description[:50] }}{% if transaction.description|length > 50 %}...{% endif %}</td>
                                <td class="{% if transaction.transaction_type == 'دخل' %}text-success{% else %}text-danger{% endif %}">
                                    {{ "%.2f"|format(transaction.amount) }} د.أ
                                </td>
                                <td>
                                    {% if transaction.cattle_id %}
                                        <a href="{{ url_for('cattle_detail', id=transaction.cattle_id) }}">
                                            {{ transaction.cattle.tag_number }}
                                        </a>
                                    {% else %}
                                        عام
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('view_financial_transaction', transaction_id=transaction.id) }}" class="btn btn-sm btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('edit_financial_transaction', transaction_id=transaction.id) }}" class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" action="{{ url_for('delete_financial_transaction', transaction_id=transaction.id) }}" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذه المعاملة المالية؟')">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد معاملات مالية</h5>
                    <p class="text-muted">ابدأ بإضافة أول معاملة مالية</p>
                    <a href="{{ url_for('add_financial_transaction') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة معاملة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- رسم بياني للإيرادات (مؤقت) -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-area"></i> الرسم البياني للإيرادات
            </div>
            <div class="card-body">
                <div class="text-center py-4">
                    <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">الرسم البياني قيد التطوير</h5>
                    <p class="text-muted">سيتم إضافة رسوم بيانية تفاعلية قريباً</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<!-- Modal تحديث سعر الحليب -->
<div class="modal fade" id="milkPriceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-tint"></i> تحديث سعر الحليب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('update_milk_price') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="current_price_display" class="form-label">السعر الحالي</label>
                        <input type="text" class="form-control" id="current_price_display" value="{{ "%.2f"|format(milk_price) }} د.أ/لتر" readonly>
                    </div>

                    <div class="mb-3">
                        <label for="milk_price_financial" class="form-label">السعر الجديد (د.أ/لتر) *</label>
                        <input type="number" step="0.01" min="0" class="form-control" id="milk_price_financial" name="milk_price" value="{{ milk_price }}" required>
                        <div class="form-text">أدخل السعر الجديد لكل لتر حليب</div>
                    </div>

                    <div class="mb-3">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>تنبيه:</strong> تحديث السعر سيؤثر على حسابات الإيرادات المستقبلية.
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> تحديث السعر
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{% block scripts %}
<script>
    // حساب الإيرادات المتوقعة
    function calculateRevenue() {
        const quantity = parseFloat(document.getElementById('milk_quantity').value) || 0;
        const price = {{ milk_price }};
        const revenue = (quantity * price).toFixed(2);

        document.getElementById('expected_revenue').value = revenue + ' د.أ';
    }

    // تحديث معاينة السعر عند تغييره
    if (document.getElementById('milk_price_financial')) {
        document.getElementById('milk_price_financial').addEventListener('input', function() {
            const newPrice = parseFloat(this.value) || 0;
            document.getElementById('price_used').value = newPrice.toFixed(2) + ' د.أ/لتر';
            calculateRevenue();
        });
    }

    // حساب أولي
    document.addEventListener('DOMContentLoaded', function() {
        calculateRevenue();
    });
</script>
{% endblock %}
