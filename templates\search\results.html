{% extends "base.html" %}

{% block title %}نتائج البحث - نظام إدارة الأبقار{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-search"></i> نتائج البحث
            {% if query %}
                عن: "{{ query }}"
            {% endif %}
        </h1>
    </div>
</div>

<!-- شريط البحث -->
<div class="row mb-4">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-body">
                <form method="GET">
                    <div class="input-group">
                        <input type="text" class="form-control" name="q" value="{{ query }}" 
                               placeholder="ابحث عن بقرة برقم العلامة، الاسم، أو السلالة...">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- نتائج البحث -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-list"></i> النتائج ({{ results|length }})
            </div>
            <div class="card-body">
                {% if results %}
                    <div class="list-group">
                        {% for result in results %}
                        <a href="{{ result.url }}" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    {% if result.type == 'cattle' %}
                                        <i class="fas fa-cow text-primary"></i>
                                    {% endif %}
                                    {{ result.title }}
                                </h6>
                                <small class="text-muted">{{ result.type }}</small>
                            </div>
                            <p class="mb-1">{{ result.description }}</p>
                        </a>
                        {% endfor %}
                    </div>
                {% elif query %}
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-5x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد نتائج</h4>
                        <p class="text-muted">لم يتم العثور على نتائج للبحث "{{ query }}"</p>
                        <p class="text-muted">جرب البحث بكلمات مختلفة أو تحقق من الإملاء</p>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-5x text-muted mb-3"></i>
                        <h4 class="text-muted">ابدأ البحث</h4>
                        <p class="text-muted">أدخل كلمة البحث في الحقل أعلاه</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- اقتراحات البحث -->
{% if not results and query %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-lightbulb"></i> اقتراحات البحث
            </div>
            <div class="card-body">
                <p><strong>نصائح للبحث الفعال:</strong></p>
                <ul>
                    <li>استخدم رقم العلامة الكامل (مثل: C001)</li>
                    <li>ابحث باسم البقرة</li>
                    <li>ابحث بنوع السلالة (هولشتاين، جيرسي، إلخ)</li>
                    <li>تأكد من الإملاء الصحيح</li>
                </ul>
                
                <div class="mt-3">
                    <h6>روابط سريعة:</h6>
                    <a href="{{ url_for('cattle_list') }}" class="btn btn-outline-primary btn-sm me-2">
                        <i class="fas fa-cow"></i> جميع الأبقار
                    </a>
                    <a href="{{ url_for('health_records') }}" class="btn btn-outline-success btn-sm me-2">
                        <i class="fas fa-heartbeat"></i> السجلات الصحية
                    </a>
                    <a href="{{ url_for('milk_production') }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-tint"></i> إنتاج الحليب
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
