from app import app, db, get_setting, set_setting

if __name__ == '__main__':
    print("🐄 نظام إدارة الأبقار المتطور")
    print("=" * 50)

    with app.app_context():
        # إنشاء الجداول
        db.create_all()

        # تحديث سعر الحليب إلى 0.4 دينار
        set_setting('milk_price', '0.40')
        set_setting('milk_price_updated', '2024/01/01 12:00')

        # التحقق من السعر المحدث
        current_price = get_setting('milk_price', '0.40')
        print(f"✅ سعر الحليب المحدث: {current_price} د.أ/لتر")

        print("🌐 النظام جاهز على: http://localhost:5000")
        print("📊 تقرير الأداء: http://localhost:5000/reports/performance")
        print("⚙️ الإعدادات: http://localhost:5000/settings")
        print("=" * 50)

    print("تشغيل النظام...")
    app.run(debug=True, host='0.0.0.0', port=5000)
