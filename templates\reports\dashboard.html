{% extends "base.html" %}

{% block title %}لوحة التحكم المتقدمة - نظام إدارة الأبقار{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-tachometer-alt"></i> لوحة التحكم المتقدمة</h1>
            <div>
                <button class="btn btn-primary me-2" onclick="saveDashboard()">
                    <i class="fas fa-save"></i> حفظ اللوحة
                </button>
                <button class="btn btn-success me-2" onclick="exportDashboard()">
                    <i class="fas fa-download"></i> تصدير البيانات
                </button>
                <button class="btn btn-info me-2" onclick="printDashboard()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <a href="{{ url_for('index') }}" class="btn btn-secondary">
                    <i class="fas fa-home"></i> الرئيسية
                </a>
            </div>
        </div>
    </div>
</div>

<!-- مؤشرات الأداء الرئيسية -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card text-white bg-primary">
            <div class="card-body text-center">
                <i class="fas fa-cow fa-2x mb-2"></i>
                <h3>{{ total_cattle }}</h3>
                <p class="mb-0">إجمالي الأبقار</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-2">
        <div class="card text-white bg-success">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h3>{{ active_cattle }}</h3>
                <p class="mb-0">أبقار نشطة</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-2">
        <div class="card text-white bg-info">
            <div class="card-body text-center">
                <i class="fas fa-tint fa-2x mb-2"></i>
                <h3>{{ daily_milk }}</h3>
                <p class="mb-0">إنتاج يومي (لتر)</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-2">
        <div class="card text-white bg-warning">
            <div class="card-body text-center">
                <i class="fas fa-heart fa-2x mb-2"></i>
                <h3>{{ pregnant_cattle }}</h3>
                <p class="mb-0">أبقار حوامل</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-2">
        <div class="card text-white bg-danger">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h3>{{ health_alerts }}</h3>
                <p class="mb-0">تنبيهات صحية</p>
            </div>
        </div>
    </div>

    <div class="col-md-2">
        <div class="card text-white bg-dark">
            <div class="card-body text-center">
                <i class="fas fa-coins fa-2x mb-2"></i>
                <h3 class="milk-price-display">{{ "%.2f"|format(milk_price) }}</h3>
                <p class="mb-0">سعر اللتر (د.أ)</p>
                <button class="btn btn-sm btn-outline-light mt-1" data-bs-toggle="modal" data-bs-target="#milkPriceModal">
                    <i class="fas fa-edit"></i> تحديث
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-md-2">
        <div class="card text-white bg-dark">
            <div class="card-body text-center">
                <i class="fas fa-coins fa-2x mb-2"></i>
                <h3>{{ monthly_profit }}</h3>
                <p class="mb-0">ربح شهري (د.أ)</p>
            </div>
        </div>
    </div>
</div>

<!-- الرسوم البيانية -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-line"></i> إنتاج الحليب الأسبوعي
            </div>
            <div class="card-body">
                <canvas id="milkChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-pie"></i> توزيع السلالات
            </div>
            <div class="card-body">
                <canvas id="breedChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- التنبيهات والمهام -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <i class="fas fa-bell"></i> التنبيهات المهمة
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-syringe"></i>
                    <strong>تطعيمات مستحقة:</strong> لا توجد تطعيمات مستحقة
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-calendar"></i>
                    <strong>فحوصات دورية:</strong> لا توجد فحوصات مستحقة
                </div>
                <div class="alert alert-success">
                    <i class="fas fa-baby"></i>
                    <strong>ولادات متوقعة:</strong> لا توجد ولادات متوقعة
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-info text-white">
                <i class="fas fa-tasks"></i> المهام اليومية
            </div>
            <div class="card-body">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="task1">
                    <label class="form-check-label" for="task1">
                        تسجيل إنتاج الحليب الصباحي
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="task2">
                    <label class="form-check-label" for="task2">
                        فحص الأبقار الحوامل
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="task3">
                    <label class="form-check-label" for="task3">
                        تنظيف الحظائر
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="task4">
                    <label class="form-check-label" for="task4">
                        تحضير العلف
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="task5">
                    <label class="form-check-label" for="task5">
                        تسجيل إنتاج الحليب المسائي
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- أفضل الأبقار أداءً -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-trophy"></i> أفضل الأبقار أداءً هذا الشهر
            </div>
            <div class="card-body">
                <div class="text-center py-4">
                    <i class="fas fa-award fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">تقرير الأداء قيد التطوير</h5>
                    <p class="text-muted">سيتم عرض أفضل الأبقار من حيث الإنتاج والصحة</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // رسم بياني لإنتاج الحليب (بيانات تجريبية)
    const milkCtx = document.getElementById('milkChart').getContext('2d');
    const milkChart = new Chart(milkCtx, {
        type: 'line',
        data: {
            labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
            datasets: [{
                label: 'إنتاج الحليب (لتر)',
                data: [45, 52, 48, 61, 55, 67, 58],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'إنتاج الحليب الأسبوعي'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'لتر'
                    }
                }
            }
        }
    });

    // رسم بياني لتوزيع السلالات
    const breedCtx = document.getElementById('breedChart').getContext('2d');
    const breedChart = new Chart(breedCtx, {
        type: 'doughnut',
        data: {
            labels: ['هولشتاين', 'جيرسي', 'سيمنتال', 'أخرى'],
            datasets: [{
                data: [40, 30, 20, 10],
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'توزيع السلالات'
                },
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // وظائف إضافية للوحة التحكم
    function saveDashboard() {
        if (confirm('هل تريد حفظ حالة لوحة التحكم الحالية؟')) {
            // حفظ إعدادات اللوحة
            localStorage.setItem('dashboard_saved', new Date().toISOString());
            localStorage.setItem('dashboard_data', JSON.stringify({
                total_cattle: {{ total_cattle }},
                active_cattle: {{ active_cattle }},
                pregnant_cattle: {{ pregnant_cattle }},
                daily_milk: {{ daily_milk }},
                health_alerts: {{ health_alerts }},
                monthly_profit: {{ monthly_profit }}
            }));
            alert('تم حفظ لوحة التحكم بنجاح!');
        }
    }

    function exportDashboard() {
        // تصدير بيانات اللوحة
        const data = {
            total_cattle: {{ total_cattle }},
            active_cattle: {{ active_cattle }},
            pregnant_cattle: {{ pregnant_cattle }},
            daily_milk: {{ daily_milk }},
            health_alerts: {{ health_alerts }},
            monthly_profit: {{ monthly_profit }},
            export_date: new Date().toISOString(),
            export_time: new Date().toLocaleString('ar-JO')
        };

        const dataStr = JSON.stringify(data, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `dashboard_data_${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        URL.revokeObjectURL(url);
        alert('تم تصدير بيانات لوحة التحكم بنجاح!');
    }

    function printDashboard() {
        window.print();
    }

    // تحديث معاينة الإيرادات عند تغيير سعر الحليب
    if (document.getElementById('milk_price_dashboard')) {
        document.getElementById('milk_price_dashboard').addEventListener('input', function() {
            const newPrice = parseFloat(this.value) || 0;
            const dailyMilk = {{ daily_milk }};
            const newRevenue = (newPrice * dailyMilk).toFixed(2);

            if (document.getElementById('daily_revenue_preview')) {
                document.getElementById('daily_revenue_preview').textContent = newRevenue + ' د.أ';
            }
        });
    }
</script>
{% endblock %}

<!-- Modal تحديث سعر الحليب -->
<div class="modal fade" id="milkPriceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-tint"></i> تحديث سعر الحليب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('update_milk_price') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="current_price_display" class="form-label">السعر الحالي</label>
                        <input type="text" class="form-control" id="current_price_display" value="{{ "%.2f"|format(milk_price) }} د.أ/لتر" readonly>
                    </div>

                    <div class="mb-3">
                        <label for="milk_price_dashboard" class="form-label">السعر الجديد (د.أ/لتر) *</label>
                        <input type="number" step="0.01" min="0" class="form-control" id="milk_price_dashboard" name="milk_price" value="{{ milk_price }}" required>
                        <div class="form-text">أدخل السعر الجديد لكل لتر حليب</div>
                    </div>

                    <div class="mb-3">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>تأثير التحديث:</strong> سيتم تحديث السعر في جميع أنحاء النظام وإعادة حساب الإيرادات.
                        </div>
                    </div>

                    <!-- معاينة التأثير على الإيرادات -->
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-calculator"></i> تأثير على الإيرادات اليومية
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-12">
                                    <small class="text-muted">إنتاج اليوم: {{ daily_milk }} لتر</small>
                                    <div id="daily_revenue_preview" class="fw-bold text-success">
                                        {{ "%.2f"|format(daily_milk * milk_price) }} د.أ
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> تحديث السعر
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
