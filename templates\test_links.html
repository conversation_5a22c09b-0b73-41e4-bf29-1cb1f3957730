{% extends "base.html" %}

{% block title %}اختبار الروابط - نظام إدارة الأبقار{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-link"></i> اختبار جميع الروابط
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-cow"></i> روابط الأبقار
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('cattle_list') }}" class="btn btn-outline-primary">
                        <i class="fas fa-list"></i> قائمة الأبقار
                    </a>
                    <a href="{{ url_for('add_cattle') }}" class="btn btn-outline-success">
                        <i class="fas fa-plus"></i> إضافة بقرة جديدة
                    </a>
                    {% if cattle_id %}
                    <a href="{{ url_for('cattle_detail', id=cattle_id) }}" class="btn btn-outline-info">
                        <i class="fas fa-eye"></i> تفاصيل البقرة الأولى
                    </a>
                    <a href="{{ url_for('add_health_record', cattle_id=cattle_id) }}" class="btn btn-outline-warning">
                        <i class="fas fa-syringe"></i> إضافة سجل صحي للبقرة الأولى
                    </a>
                    <a href="{{ url_for('add_milk_record', cattle_id=cattle_id) }}" class="btn btn-outline-info">
                        <i class="fas fa-tint"></i> تسجيل حليب للبقرة الأولى
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-line"></i> روابط التقارير
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('health_records') }}" class="btn btn-outline-danger">
                        <i class="fas fa-heartbeat"></i> السجلات الصحية
                    </a>
                    <a href="{{ url_for('milk_production') }}" class="btn btn-outline-info">
                        <i class="fas fa-tint"></i> إنتاج الحليب
                    </a>
                    <a href="{{ url_for('breeding_records') }}" class="btn btn-outline-success">
                        <i class="fas fa-heart"></i> سجلات التكاثر
                    </a>
                    <a href="{{ url_for('financial_reports') }}" class="btn btn-outline-warning">
                        <i class="fas fa-chart-line"></i> التقارير المالية
                    </a>
                    <a href="{{ url_for('advanced_dashboard') }}" class="btn btn-outline-primary">
                        <i class="fas fa-tachometer-alt"></i> لوحة التحكم المتقدمة
                    </a>
                    <a href="{{ url_for('settings') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-cog"></i> الإعدادات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> معلومات الاختبار
            </div>
            <div class="card-body">
                <p><strong>الهدف:</strong> التأكد من أن جميع الروابط تعمل بشكل صحيح</p>
                <p><strong>التعليمات:</strong></p>
                <ol>
                    <li>انقر على كل رابط للتأكد من أنه يعمل</li>
                    <li>إذا ظهرت رسالة "قيد التطوير" فهذا يعني أن الرابط يحتاج إصلاح</li>
                    <li>إذا فتحت الصفحة بشكل طبيعي فالرابط يعمل</li>
                </ol>
                
                <div class="alert alert-success mt-3">
                    <i class="fas fa-check-circle"></i>
                    <strong>ملاحظة:</strong> إذا كانت جميع الروابط تعمل، فالنظام جاهز للاستخدام!
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
