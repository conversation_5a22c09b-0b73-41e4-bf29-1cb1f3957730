// نظام إدارة الأبقار - ملف JavaScript الأساسي

// تحديث الوقت الحالي
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar-JO');
    const timeElements = document.querySelectorAll('.current-time');
    timeElements.forEach(element => {
        element.textContent = timeString;
    });
}

// تشغيل تحديث الوقت كل دقيقة
setInterval(updateCurrentTime, 60000);

// تحديث الوقت عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateCurrentTime();
});

// تأكيد الحذف
function confirmDelete(message) {
    return confirm(message || 'هل أنت متأكد من الحذف؟');
}

// تنسيق الأرقام
function formatNumber(number, decimals = 2) {
    return parseFloat(number).toFixed(decimals);
}

// تنسيق العملة
function formatCurrency(amount) {
    return formatNumber(amount, 2) + ' د.أ';
}

// إظهار رسائل التحميل
function showLoading(message = 'جاري التحميل...') {
    // يمكن تحسين هذا لاحقاً
    console.log(message);
}

// إخفاء رسائل التحميل
function hideLoading() {
    // يمكن تحسين هذا لاحقاً
    console.log('تم الانتهاء من التحميل');
}
