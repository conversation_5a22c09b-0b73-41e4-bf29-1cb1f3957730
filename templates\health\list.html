{% extends "base.html" %}

{% block title %}السجلات الصحية - نظام إدارة الأبقار{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-heartbeat"></i> السجلات الصحية</h1>
            <a href="{{ url_for('select_cattle_for_health') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة سجل جديد
            </a>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body text-center">
                <i class="fas fa-syringe fa-2x mb-2"></i>
                <h4>{{ records|selectattr("record_type", "equalto", "تطعيم")|list|length }}</h4>
                <p class="mb-0">تطعيمات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body text-center">
                <i class="fas fa-stethoscope fa-2x mb-2"></i>
                <h4>{{ records|selectattr("record_type", "equalto", "فحص دوري")|list|length }}</h4>
                <p class="mb-0">فحوصات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body text-center">
                <i class="fas fa-pills fa-2x mb-2"></i>
                <h4>{{ records|selectattr("record_type", "equalto", "علاج")|list|length }}</h4>
                <p class="mb-0">علاجات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-danger">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h4>0</h4>
                <p class="mb-0">تنبيهات مستحقة</p>
            </div>
        </div>
    </div>
</div>

<!-- جدول السجلات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-list"></i> جميع السجلات الصحية
            </div>
            <div class="card-body">
                {% if records %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>التاريخ</th>
                                <th>البقرة</th>
                                <th>نوع السجل</th>
                                <th>الوصف</th>
                                <th>الطبيب البيطري</th>
                                <th>التكلفة</th>
                                <th>التاريخ المستحق</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in records %}
                            <tr>
                                <td>{{ record.date.strftime('%Y/%m/%d') if record.date else '-' }}</td>
                                <td>
                                    <a href="{{ url_for('cattle_detail', id=record.cattle.id) }}" class="text-decoration-none">
                                        <strong>{{ record.cattle.tag_number }}</strong>
                                        {% if record.cattle.name %}
                                            <br><small class="text-muted">{{ record.cattle.name }}</small>
                                        {% endif %}
                                    </a>
                                </td>
                                <td>
                                    {% if record.record_type == 'تطعيم' %}
                                        <span class="badge bg-info">{{ record.record_type }}</span>
                                    {% elif record.record_type == 'علاج' %}
                                        <span class="badge bg-warning">{{ record.record_type }}</span>
                                    {% elif record.record_type == 'فحص دوري' %}
                                        <span class="badge bg-success">{{ record.record_type }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ record.record_type }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ record.description[:50] }}{% if record.description|length > 50 %}...{% endif %}</td>
                                <td>{{ record.veterinarian or '-' }}</td>
                                <td class="text-end">
                                    {% if record.cost %}
                                        <span class="currency">{{ "%.2f"|format(record.cost) }} د.أ</span>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if record.next_due_date %}
                                        {% set days_until = record.next_due_date | days_until %}
                                        {% if days_until < 0 %}
                                            <span class="badge bg-danger">متأخر {{ -days_until }} يوم</span>
                                        {% elif days_until <= 7 %}
                                            <span class="badge bg-warning">{{ days_until }} يوم</span>
                                        {% else %}
                                            <span class="badge bg-success">{{ record.next_due_date.strftime('%Y/%m/%d') }}</span>
                                        {% endif %}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('view_health_record', record_id=record.id) }}" class="btn btn-sm btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('edit_health_record', record_id=record.id) }}" class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" action="{{ url_for('delete_health_record', record_id=record.id) }}" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا السجل؟')">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-heartbeat fa-5x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد سجلات صحية</h4>
                    <p class="text-muted">ابدأ بإضافة أول سجل صحي</p>
                    <a href="{{ url_for('select_cattle_for_health') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة سجل صحي
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
