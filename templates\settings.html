{% extends "base.html" %}

{% block title %}الإعدادات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-cog"></i> إعدادات النظام</h1>
            <a href="{{ url_for('index') }}" class="btn btn-secondary">
                <i class="fas fa-home"></i> الرئيسية
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- إعدادات الأسعار -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-coins"></i> إعدادات الأسعار
            </div>
            <div class="card-body">
                <!-- سعر الحليب -->
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <label class="form-label mb-0">سعر الحليب (د.أ/لتر)</label>
                        <button class="btn btn-sm btn-outline-primary" onclick="editMilkPrice()">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                    </div>
                    
                    <div class="input-group">
                        <input type="number" step="0.01" class="form-control" id="milk_price_setting" value="{{ milk_price }}" readonly>
                        <span class="input-group-text">د.أ/لتر</span>
                    </div>
                    
                    <div class="form-text">
                        آخر تحديث: {{ milk_price_updated or 'غير محدد' }}
                    </div>
                    
                    <!-- معاينة سريعة للإيرادات -->
                    <div class="mt-3 p-2 bg-light rounded">
                        <small class="text-muted">معاينة الإيرادات:</small>
                        <div class="row text-center">
                            <div class="col-4">
                                <small>10 لتر</small>
                                <div class="fw-bold text-success">{{ "%.2f"|format(milk_price * 10) }} د.أ</div>
                            </div>
                            <div class="col-4">
                                <small>50 لتر</small>
                                <div class="fw-bold text-success">{{ "%.2f"|format(milk_price * 50) }} د.أ</div>
                            </div>
                            <div class="col-4">
                                <small>100 لتر</small>
                                <div class="fw-bold text-success">{{ "%.2f"|format(milk_price * 100) }} د.أ</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- أسعار أخرى (قيد التطوير) -->
                <div class="mb-3">
                    <label class="form-label">أسعار أخرى</label>
                    <div class="list-group">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <span>سعر بيع البقرة</span>
                            <span class="badge bg-secondary">قيد التطوير</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <span>سعر بيع العجل</span>
                            <span class="badge bg-secondary">قيد التطوير</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- إعدادات النظام -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-cogs"></i> إعدادات النظام
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('save_settings') }}">
                    <div class="mb-3">
                        <label for="farm_name" class="form-label">اسم المزرعة</label>
                        <input type="text" class="form-control" id="farm_name" name="farm_name" value="{{ farm_name or '' }}">
                    </div>
                    
                    <div class="mb-3">
                        <label for="owner_name" class="form-label">اسم المالك</label>
                        <input type="text" class="form-control" id="owner_name" name="owner_name" value="{{ owner_name or '' }}">
                    </div>
                    
                    <div class="mb-3">
                        <label for="phone" class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" id="phone" name="phone" value="{{ phone or '' }}">
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="2">{{ address or '' }}</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="currency" class="form-label">العملة</label>
                        <select class="form-select" id="currency" name="currency">
                            <option value="د.أ" {{ 'selected' if currency == 'د.أ' }}>دينار أردني (د.أ)</option>
                            <option value="ر.س" {{ 'selected' if currency == 'ر.س' }}>ريال سعودي (ر.س)</option>
                            <option value="د.ك" {{ 'selected' if currency == 'د.ك' }}>دينار كويتي (د.ك)</option>
                            <option value="$" {{ 'selected' if currency == '$' }}>دولار أمريكي ($)</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ الإعدادات
                    </button>
                </form>
            </div>
        </div>
        
        <!-- إحصائيات النظام -->
        <div class="card mt-4">
            <div class="card-header">
                <i class="fas fa-chart-bar"></i> إحصائيات النظام
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <i class="fas fa-cow text-primary"></i>
                            <div class="fw-bold">{{ total_cattle or 0 }}</div>
                            <small class="text-muted">إجمالي الأبقار</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <i class="fas fa-tint text-info"></i>
                            <div class="fw-bold">{{ total_milk_records or 0 }}</div>
                            <small class="text-muted">سجلات الحليب</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <i class="fas fa-money-bill text-success"></i>
                            <div class="fw-bold">{{ total_financial_records or 0 }}</div>
                            <small class="text-muted">المعاملات المالية</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <i class="fas fa-heartbeat text-danger"></i>
                            <div class="fw-bold">{{ total_health_records or 0 }}</div>
                            <small class="text-muted">السجلات الصحية</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function editMilkPrice() {
        const currentPrice = document.getElementById('milk_price_setting').value;
        const newPrice = prompt('أدخل السعر الجديد لكل لتر حليب (د.أ):', currentPrice);
        
        if (newPrice !== null && !isNaN(newPrice) && parseFloat(newPrice) >= 0) {
            // إرسال طلب تحديث السعر
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = "{{ url_for('update_milk_price') }}";
            
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'milk_price';
            input.value = newPrice;
            
            form.appendChild(input);
            document.body.appendChild(form);
            form.submit();
        } else if (newPrice !== null) {
            alert('يرجى إدخال سعر صحيح أكبر من أو يساوي الصفر!');
        }
    }
</script>
{% endblock %}
