{% extends "base.html" %}

{% block title %}المواد العلفية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-wheat-awn"></i> المواد العلفية</h1>
            <div>
                <a href="{{ url_for('add_feed_ingredient') }}" class="btn btn-primary me-2">
                    <i class="fas fa-plus"></i> إضافة مادة علفية
                </a>
                <button type="button" class="btn btn-success" onclick="addBasicIngredients()">
                    <i class="fas fa-magic"></i> إضافة مواد أساسية
                </button>
            </div>
        </div>
    </div>
</div>

{% if ingredients %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-list"></i> قائمة المواد العلفية ({{ ingredients|length }})
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>اسم المادة</th>
                                <th>الفئة</th>
                                <th>البروتين %</th>
                                <th>الطاقة MJ/kg</th>
                                <th>الألياف %</th>
                                <th>السعر/كغ</th>
                                <th>المورد</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for ingredient in ingredients %}
                            <tr>
                                <td><strong>{{ ingredient.name }}</strong></td>
                                <td>
                                    {% if ingredient.category == 'حبوب' %}
                                        <span class="badge bg-warning">{{ ingredient.category }}</span>
                                    {% elif ingredient.category == 'بروتين' %}
                                        <span class="badge bg-success">{{ ingredient.category }}</span>
                                    {% elif ingredient.category == 'فيتامينات' %}
                                        <span class="badge bg-info">{{ ingredient.category }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ ingredient.category or 'غير محدد' }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ "%.1f"|format(ingredient.protein_percentage) if ingredient.protein_percentage else '-' }}%</td>
                                <td>{{ "%.1f"|format(ingredient.energy_value) if ingredient.energy_value else '-' }}</td>
                                <td>{{ "%.1f"|format(ingredient.fiber_percentage) if ingredient.fiber_percentage else '-' }}%</td>
                                <td>{{ "%.2f"|format(ingredient.price_per_kg) if ingredient.price_per_kg else '-' }} د.أ</td>
                                <td>{{ ingredient.supplier or '-' }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-outline-primary" title="عرض" onclick="viewIngredient({{ ingredient.id }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <a href="{{ url_for('edit_feed_ingredient', ingredient_id=ingredient.id) }}" class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" action="{{ url_for('delete_feed_ingredient', ingredient_id=ingredient.id) }}" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذه المادة العلفية؟\n\nتأكد من عدم استخدامها في أي خلطة!')">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-wheat-awn fa-3x text-muted mb-3"></i>
                <h4>لا توجد مواد علفية</h4>
                <p class="text-muted">ابدأ بإضافة المواد العلفية لإنشاء الخلطات</p>
                <div class="d-flex gap-2 justify-content-center">
                    <button type="button" class="btn btn-success" onclick="addBasicIngredients()">
                        <i class="fas fa-magic"></i> إضافة مواد أساسية
                    </button>
                    <a href="{{ url_for('add_feed_ingredient') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة مادة مخصصة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
    function viewIngredient(id) {
        // عرض تفاصيل المادة العلفية - قيد التطوير
        alert('عرض تفاصيل المادة العلفية رقم ' + id + ' - قيد التطوير');
    }

    function addBasicIngredients() {
        if (confirm('هل تريد إضافة المواد العلفية الأساسية؟\n\nسيتم إضافة: شعير، ذرة، كسبة فول الصويا، برسيم، نخالة قمح، فيتامينات، ملح')) {
            // إرسال طلب لإضافة المواد الأساسية
            fetch('/feed/ingredients/add-basic', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم إضافة المواد العلفية الأساسية بنجاح!');
                    location.reload();
                } else {
                    alert('حدث خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                alert('حدث خطأ في إضافة المواد العلفية!');
            });
        }
    }
</script>
{% endblock %}
