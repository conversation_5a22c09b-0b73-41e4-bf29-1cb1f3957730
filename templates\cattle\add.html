{% extends "base.html" %}

{% block title %}إضافة بقرة جديدة - نظام إدارة الأبقار{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-plus"></i> إضافة بقرة جديدة</h1>
            <a href="{{ url_for('cattle_list') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-cow"></i> بيانات البقرة
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="tag_number" class="form-label">رقم العلامة *</label>
                            <input type="text" class="form-control" id="tag_number" name="tag_number" required>
                            <div class="form-text">رقم فريد لتمييز البقرة</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">الاسم</label>
                            <input type="text" class="form-control" id="name" name="name">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="breed" class="form-label">السلالة</label>
                            <select class="form-select" id="breed" name="breed">
                                <option value="">اختر السلالة</option>
                                <option value="هولشتاين">هولشتاين</option>
                                <option value="جيرسي">جيرسي</option>
                                <option value="براون سويس">براون سويس</option>
                                <option value="سيمنتال">سيمنتال</option>
                                <option value="شاروليه">شاروليه</option>
                                <option value="أنجوس">أنجوس</option>
                                <option value="بلدي">بلدي</option>
                                <option value="مختلط">مختلط</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="gender" class="form-label">الجنس *</label>
                            <select class="form-select" id="gender" name="gender" required>
                                <option value="">اختر الجنس</option>
                                <option value="أنثى">أنثى</option>
                                <option value="ذكر">ذكر</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="birth_date" class="form-label">تاريخ الولادة</label>
                            <input type="date" class="form-control" id="birth_date" name="birth_date">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="weight" class="form-label">الوزن (كغ)</label>
                            <input type="number" step="0.1" class="form-control" id="weight" name="weight">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="color" class="form-label">اللون</label>
                            <select class="form-select" id="color" name="color">
                                <option value="">اختر اللون</option>
                                <option value="أسود">أسود</option>
                                <option value="أبيض">أبيض</option>
                                <option value="بني">بني</option>
                                <option value="أحمر">أحمر</option>
                                <option value="أسود وأبيض">أسود وأبيض</option>
                                <option value="بني وأبيض">بني وأبيض</option>
                                <option value="أحمر وأبيض">أحمر وأبيض</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="purchase_price" class="form-label">سعر الشراء (دينار أردني)</label>
                            <input type="number" step="0.01" class="form-control" id="purchase_price" name="purchase_price">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="purchase_date" class="form-label">تاريخ الشراء</label>
                            <input type="date" class="form-control" id="purchase_date" name="purchase_date">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('cattle_list') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ البيانات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // تعيين التاريخ الحالي كافتراضي لتاريخ الشراء
    document.getElementById('purchase_date').value = moment().format('YYYY-MM-DD');
</script>
{% endblock %}
