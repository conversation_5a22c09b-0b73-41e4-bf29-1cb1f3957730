{% extends "base.html" %}

{% block title %}تعديل المعاملة المالية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-edit"></i> تعديل المعاملة المالية</h1>
            <a href="{{ url_for('financial_reports') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> العودة للتقارير
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-money-bill-wave"></i> تعديل بيانات المعاملة
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="transaction_type" class="form-label">نوع المعاملة *</label>
                            <select class="form-select" id="transaction_type" name="transaction_type" required>
                                <option value="دخل" {{ 'selected' if transaction.transaction_type == 'دخل' }}>دخل</option>
                                <option value="مصروف" {{ 'selected' if transaction.transaction_type == 'مصروف' }}>مصروف</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label">الفئة *</label>
                            <select class="form-select" id="category" name="category" required>
                                <option value="">اختر الفئة</option>
                                <optgroup label="الإيرادات">
                                    <option value="بيع حليب" {{ 'selected' if transaction.category == 'بيع حليب' }}>بيع حليب</option>
                                    <option value="بيع أبقار" {{ 'selected' if transaction.category == 'بيع أبقار' }}>بيع أبقار</option>
                                    <option value="بيع عجول" {{ 'selected' if transaction.category == 'بيع عجول' }}>بيع عجول</option>
                                    <option value="إيرادات أخرى" {{ 'selected' if transaction.category == 'إيرادات أخرى' }}>إيرادات أخرى</option>
                                </optgroup>
                                <optgroup label="المصروفات">
                                    <option value="علف" {{ 'selected' if transaction.category == 'علف' }}>علف</option>
                                    <option value="أدوية" {{ 'selected' if transaction.category == 'أدوية' }}>أدوية وعلاج</option>
                                    <option value="رواتب" {{ 'selected' if transaction.category == 'رواتب' }}>رواتب</option>
                                    <option value="صيانة" {{ 'selected' if transaction.category == 'صيانة' }}>صيانة</option>
                                    <option value="كهرباء" {{ 'selected' if transaction.category == 'كهرباء' }}>كهرباء ومياه</option>
                                    <option value="مصروفات أخرى" {{ 'selected' if transaction.category == 'مصروفات أخرى' }}>مصروفات أخرى</option>
                                </optgroup>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="amount" class="form-label">المبلغ (د.أ) *</label>
                            <input type="number" step="0.01" class="form-control" id="amount" name="amount" value="{{ transaction.amount }}" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="date" class="form-label">التاريخ *</label>
                            <input type="date" class="form-control" id="date" name="date" value="{{ transaction.date.strftime('%Y-%m-%d') if transaction.date else '' }}" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف *</label>
                        <input type="text" class="form-control" id="description" name="description" value="{{ transaction.description }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="cattle_id" class="form-label">البقرة المرتبطة (اختياري)</label>
                        <select class="form-select" id="cattle_id" name="cattle_id">
                            <option value="">لا توجد بقرة محددة</option>
                            {% for cattle in cattle_list %}
                            <option value="{{ cattle.id }}" {{ 'selected' if transaction.cattle_id == cattle.id }}>
                                {{ cattle.tag_number }} - {{ cattle.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3">{{ transaction.notes or '' }}</textarea>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('financial_reports') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ التعديلات
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="card mt-4">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> معلومات المعاملة
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>تاريخ الإنشاء:</strong>
                        {{ transaction.created_at.strftime('%Y/%m/%d %H:%M') if transaction.created_at else 'غير محدد' }}
                    </div>
                    <div class="col-md-6">
                        <strong>رقم المعاملة:</strong>
                        #{{ transaction.id }}
                    </div>
                </div>
                
                {% if transaction.cattle_id %}
                <div class="mt-3">
                    <strong>البقرة المرتبطة:</strong>
                    <a href="{{ url_for('cattle_detail', id=transaction.cattle_id) }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-cow"></i> {{ transaction.cattle.tag_number }} - {{ transaction.cattle.name }}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // تحديث الفئات بناءً على نوع المعاملة
    document.getElementById('transaction_type').addEventListener('change', function() {
        const categorySelect = document.getElementById('category');
        const transactionType = this.value;
        
        // مسح الخيارات الحالية
        categorySelect.innerHTML = '<option value="">اختر الفئة</option>';
        
        if (transactionType === 'دخل') {
            categorySelect.innerHTML += `
                <optgroup label="الإيرادات">
                    <option value="بيع حليب">بيع حليب</option>
                    <option value="بيع أبقار">بيع أبقار</option>
                    <option value="بيع عجول">بيع عجول</option>
                    <option value="إيرادات أخرى">إيرادات أخرى</option>
                </optgroup>
            `;
        } else if (transactionType === 'مصروف') {
            categorySelect.innerHTML += `
                <optgroup label="المصروفات">
                    <option value="علف">علف</option>
                    <option value="أدوية">أدوية وعلاج</option>
                    <option value="رواتب">رواتب</option>
                    <option value="صيانة">صيانة</option>
                    <option value="كهرباء">كهرباء ومياه</option>
                    <option value="مصروفات أخرى">مصروفات أخرى</option>
                </optgroup>
            `;
        }
    });
</script>
{% endblock %}
