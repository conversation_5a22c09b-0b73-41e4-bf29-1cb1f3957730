{% extends "base.html" %}
{% block title %}اختر بقرة - إضافة سجل صحي{% endblock %}
{% block content %}
<div class="card">
  <div class="card-header"><i class="fas fa-syringe"></i> اختر بقرة لإضافة سجل صحي</div>
  <div class="card-body">
    {% if cattle %}
      <div class="list-group">
        {% for cow in cattle %}
        <a class="list-group-item list-group-item-action" href="{{ url_for('add_health_record', cattle_id=cow.id) }}">
          <strong>{{ cow.tag_number }}</strong> — {{ cow.name or 'بدون اسم' }} ({{ cow.breed or 'غير محدد' }})
        </a>
        {% endfor %}
      </div>
    {% else %}
      <div class="alert alert-warning">لا توجد أبقار. أضف بقرة أولاً.</div>
      <a href="{{ url_for('add_cattle') }}" class="btn btn-primary"><i class="fas fa-plus"></i> إضافة بقرة</a>
    {% endif %}
  </div>
</div>
{% endblock %}
