{% extends "base.html" %}

{% block title %}استعادة النسخة الاحتياطية - الإعدادات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-upload"></i> استعادة النسخة الاحتياطية</h1>
            <a href="{{ url_for('settings') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> العودة للإعدادات
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-exclamation-triangle text-warning"></i> تحذير مهم
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h5><i class="fas fa-exclamation-triangle"></i> تنبيه هام!</h5>
                    <p>استعادة النسخة الاحتياطية ستؤدي إلى:</p>
                    <ul>
                        <li>حذف جميع البيانات الحالية</li>
                        <li>استبدالها بالبيانات من النسخة الاحتياطية</li>
                        <li>فقدان أي تغييرات لم يتم حفظها</li>
                    </ul>
                    <p class="mb-0"><strong>هذا الإجراء لا يمكن التراجع عنه!</strong></p>
                </div>
                
                <form method="POST" enctype="multipart/form-data" onsubmit="return confirmRestore()">
                    <div class="mb-4">
                        <label for="backup_file" class="form-label">اختر ملف النسخة الاحتياطية</label>
                        <input type="file" class="form-control" id="backup_file" name="backup_file" accept=".db,.sql,.backup" required>
                        <div class="form-text">
                            الملفات المدعومة: .db, .sql, .backup
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="confirm_restore" required>
                            <label class="form-check-label" for="confirm_restore">
                                أؤكد أنني أفهم أن هذا الإجراء سيحذف جميع البيانات الحالية
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('settings') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-upload"></i> استعادة النسخة الاحتياطية
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="card mt-4">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> معلومات مفيدة
            </div>
            <div class="card-body">
                <h6>كيفية إنشاء نسخة احتياطية:</h6>
                <ol>
                    <li>اذهب إلى صفحة الإعدادات</li>
                    <li>اضغط على "نسخة احتياطية"</li>
                    <li>احفظ الملف في مكان آمن</li>
                </ol>
                
                <h6 class="mt-3">نصائح الأمان:</h6>
                <ul>
                    <li>أنشئ نسخة احتياطية قبل الاستعادة</li>
                    <li>تأكد من صحة ملف النسخة الاحتياطية</li>
                    <li>اختبر النظام بعد الاستعادة</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function confirmRestore() {
        return confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟\n\nسيتم حذف جميع البيانات الحالية!\n\nهذا الإجراء لا يمكن التراجع عنه!');
    }
</script>
{% endblock %}
