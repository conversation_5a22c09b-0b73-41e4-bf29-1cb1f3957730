{% extends "base.html" %}

{% block title %}تعديل السجل الصحي - {{ record.cattle.tag_number }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-edit"></i> تعديل السجل الصحي للبقرة: {{ record.cattle.tag_number }}</h1>
            <a href="{{ url_for('cattle_detail', id=record.cattle.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> العودة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-heartbeat"></i> تعديل بيانات السجل الصحي
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="record_type" class="form-label">نوع السجل *</label>
                            <select class="form-select" id="record_type" name="record_type" required>
                                <option value="">اختر نوع السجل</option>
                                <option value="تطعيم" {{ 'selected' if record.record_type == 'تطعيم' }}>تطعيم</option>
                                <option value="علاج" {{ 'selected' if record.record_type == 'علاج' }}>علاج</option>
                                <option value="فحص دوري" {{ 'selected' if record.record_type == 'فحص دوري' }}>فحص دوري</option>
                                <option value="جراحة" {{ 'selected' if record.record_type == 'جراحة' }}>جراحة</option>
                                <option value="أخرى" {{ 'selected' if record.record_type == 'أخرى' }}>أخرى</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="date" class="form-label">التاريخ *</label>
                            <input type="date" class="form-control" id="date" name="date" value="{{ record.date.strftime('%Y-%m-%d') if record.date }}" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف *</label>
                        <textarea class="form-control" id="description" name="description" rows="3" required>{{ record.description }}</textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="veterinarian" class="form-label">الطبيب البيطري</label>
                            <input type="text" class="form-control" id="veterinarian" name="veterinarian" value="{{ record.veterinarian or '' }}">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="cost" class="form-label">التكلفة (د.أ)</label>
                            <input type="number" step="0.01" class="form-control" id="cost" name="cost" value="{{ record.cost or '' }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="next_due_date" class="form-label">الموعد التالي</label>
                            <input type="date" class="form-control" id="next_due_date" name="next_due_date" value="{{ record.next_due_date.strftime('%Y-%m-%d') if record.next_due_date }}">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3">{{ record.notes or '' }}</textarea>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('cattle_detail', id=record.cattle.id) }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ التعديلات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
