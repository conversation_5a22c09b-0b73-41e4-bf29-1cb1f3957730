{% extends "base.html" %}

{% block title %}تسجيل إنتاج الحليب - {{ cattle.tag_number }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-tint"></i> تسجيل إنتاج الحليب للبقرة: {{ cattle.tag_number }}</h1>
            <div>
                <!-- عرض سعر الحليب الحالي -->
                <div class="d-inline-block me-3">
                    <small class="text-muted">سعر اللتر:</small>
                    <span class="badge bg-warning text-dark">
                        <i class="fas fa-coins"></i> {{ "%.2f"|format(milk_price) }} د.أ
                    </span>
                    <button type="button" class="btn btn-sm btn-outline-warning ms-1" data-bs-toggle="modal" data-bs-target="#milkPriceModal" title="تحديث سعر الحليب">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
                <a href="{{ url_for('cattle_detail', id=cattle.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-tint"></i> بيانات إنتاج الحليب
            </div>
            <div class="card-body">
                <form method="POST" id="milkForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="date" class="form-label">التاريخ *</label>
                            <input type="date" class="form-control" id="date" name="date" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="quality_grade" class="form-label">درجة الجودة</label>
                            <select class="form-select" id="quality_grade" name="quality_grade">
                                <option value="">اختر درجة الجودة</option>
                                <option value="ممتاز">ممتاز</option>
                                <option value="جيد جداً">جيد جداً</option>
                                <option value="جيد">جيد</option>
                                <option value="مقبول">مقبول</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="morning_amount" class="form-label">كمية الصباح (لتر)</label>
                            <input type="number" step="0.1" class="form-control" id="morning_amount" name="morning_amount" min="0">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="evening_amount" class="form-label">كمية المساء (لتر)</label>
                            <input type="number" step="0.1" class="form-control" id="evening_amount" name="evening_amount" min="0">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الإجمالي اليومي</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="total_display" readonly>
                                <span class="input-group-text">لتر</span>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">القيمة المقدرة</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="estimated_value" readonly>
                                <span class="input-group-text">دينار أردني</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('cattle_detail', id=cattle.id) }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ الإنتاج
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // تعيين التاريخ الحالي كافتراضي
    document.getElementById('date').value = moment().format('YYYY-MM-DD');
    
    // سعر اللتر الحالي من الإعدادات
    const MILK_PRICE_PER_LITER = {{ milk_price }};
    
    function calculateTotal() {
        const morning = parseFloat(document.getElementById('morning_amount').value) || 0;
        const evening = parseFloat(document.getElementById('evening_amount').value) || 0;
        const total = morning + evening;
        const estimatedValue = total * MILK_PRICE_PER_LITER;
        
        document.getElementById('total_display').value = total.toFixed(1);
        document.getElementById('estimated_value').value = estimatedValue.toFixed(2);
    }
    
    // ربط الحساب بتغيير القيم
    document.getElementById('morning_amount').addEventListener('input', calculateTotal);
    document.getElementById('evening_amount').addEventListener('input', calculateTotal);
    
    // حساب أولي
    calculateTotal();

    // تحديث معاينة التأثير عند تغيير السعر في Modal
    if (document.getElementById('milk_price_update')) {
        document.getElementById('milk_price_update').addEventListener('input', function() {
            const newPrice = parseFloat(this.value) || 0;
            // يمكن إضافة معاينة هنا إذا لزم الأمر
        });
    }
</script>
{% endblock %}

<!-- Modal تحديث سعر الحليب -->
<div class="modal fade" id="milkPriceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-tint"></i> تحديث سعر الحليب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('update_milk_price') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="current_price_display" class="form-label">السعر الحالي</label>
                        <input type="text" class="form-control" id="current_price_display" value="{{ "%.2f"|format(milk_price) }} د.أ/لتر" readonly>
                    </div>

                    <div class="mb-3">
                        <label for="milk_price_update" class="form-label">السعر الجديد (د.أ/لتر) *</label>
                        <input type="number" step="0.01" min="0" class="form-control" id="milk_price_update" name="milk_price" value="{{ milk_price }}" required>
                        <div class="form-text">أدخل السعر الجديد لكل لتر حليب</div>
                    </div>

                    <div class="mb-3">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>تنبيه:</strong> تحديث السعر سيؤثر على حسابات هذا النموذج فوراً.
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> تحديث السعر
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
