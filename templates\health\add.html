{% extends "base.html" %}

{% block title %}إضافة سجل صحي - {{ cattle.tag_number }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-plus"></i> إضافة سجل صحي للبقرة: {{ cattle.tag_number }}</h1>
            <a href="{{ url_for('cattle_detail', id=cattle.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> العودة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-heartbeat"></i> بيانات السجل الصحي
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="record_type" class="form-label">نوع السجل *</label>
                            <select class="form-select" id="record_type" name="record_type" required>
                                <option value="">اختر نوع السجل</option>
                                <option value="تطعيم">تطعيم</option>
                                <option value="علاج">علاج</option>
                                <option value="فحص دوري">فحص دوري</option>
                                <option value="جراحة">جراحة</option>
                                <option value="فحص حمل">فحص حمل</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="date" class="form-label">التاريخ *</label>
                            <input type="date" class="form-control" id="date" name="date" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف *</label>
                        <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="veterinarian" class="form-label">الطبيب البيطري</label>
                            <input type="text" class="form-control" id="veterinarian" name="veterinarian">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="cost" class="form-label">التكلفة (دينار أردني)</label>
                            <input type="number" step="0.01" class="form-control" id="cost" name="cost">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="next_due_date" class="form-label">التاريخ المستحق التالي</label>
                            <input type="date" class="form-control" id="next_due_date" name="next_due_date">
                            <div class="form-text">للتطعيمات الدورية أو المتابعة</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('cattle_detail', id=cattle.id) }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ السجل
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // تعيين التاريخ الحالي كافتراضي
    document.getElementById('date').value = moment().format('YYYY-MM-DD');
    
    // تحديث التاريخ المستحق بناءً على نوع السجل
    document.getElementById('record_type').addEventListener('change', function() {
        const nextDueDateField = document.getElementById('next_due_date');
        const selectedType = this.value;
        
        if (selectedType === 'تطعيم') {
            // إضافة 6 أشهر للتطعيمات
            nextDueDateField.value = moment().add(6, 'months').format('YYYY-MM-DD');
        } else if (selectedType === 'فحص دوري') {
            // إضافة 3 أشهر للفحوصات الدورية
            nextDueDateField.value = moment().add(3, 'months').format('YYYY-MM-DD');
        } else {
            nextDueDateField.value = '';
        }
    });
</script>
{% endblock %}
