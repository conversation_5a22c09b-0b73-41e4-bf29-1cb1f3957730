{% extends "base.html" %}

{% block title %}الرئيسية - نظام إدارة الأبقار{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-home"></i> {{ farm_name or 'نظام إدارة الأبقار' }}
        </h1>
    </div>
</div>

<!-- بطاقات الإحصائيات -->
<div class="row">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">إجمالي الأبقار</h6>
                    <div class="stats-number">{{ total_cattle }}</div>
                </div>
                <div>
                    <i class="fas fa-cow fa-3x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">الأبقار النشطة</h6>
                    <div class="stats-number">{{ active_cattle }}</div>
                </div>
                <div>
                    <i class="fas fa-check-circle fa-3x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">إنتاج الحليب الشهري</h6>
                    <div class="stats-number">{{ "%.1f"|format(monthly_milk) }}</div>
                    <small>لتر</small>
                </div>
                <div>
                    <i class="fas fa-tint fa-3x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">الإيرادات الشهرية</h6>
                    <div class="stats-number currency">{{ "%.2f"|format(monthly_income) }}</div>
                    <small>مسجلة فعلياً</small>
                </div>
                <div>
                    <i class="fas fa-coins fa-3x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="stats-card border-success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">قيمة الحليب الشهرية</h6>
                    <div class="stats-number text-success">{{ "%.2f"|format(monthly_milk * milk_price) }}</div>
                    <small>متوقعة ({{ "%.2f"|format(milk_price) }} د.أ/لتر)</small>
                </div>
                <div>
                    <i class="fas fa-tint fa-3x opacity-75 text-success"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الإجراءات السريعة -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-plus"></i> إجراءات سريعة
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('add_cattle') }}" class="btn btn-primary w-100">
                            <i class="fas fa-plus"></i><br>
                            إضافة بقرة جديدة
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('select_cattle_for_milk') }}" class="btn btn-success w-100">
                            <i class="fas fa-tint"></i><br>
                            تسجيل إنتاج حليب
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('select_cattle_for_health') }}" class="btn btn-info w-100">
                            <i class="fas fa-syringe"></i><br>
                            إضافة سجل صحي
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('select_cattle_for_breeding') }}" class="btn btn-warning w-100">
                            <i class="fas fa-heart"></i><br>
                            تسجيل تكاثر
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('feed_calculator') }}" class="btn btn-secondary w-100">
                            <i class="fas fa-calculator"></i><br>
                            حاسبة الخلطات
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('feed_mixes') }}" class="btn btn-dark w-100">
                            <i class="fas fa-seedling"></i><br>
                            الخلطات العلفية
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('saved_reports') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-archive"></i><br>
                            التقارير المحفوظة
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('monthly_report') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-calendar-alt"></i><br>
                            التقرير الشهري
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('performance_report') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-chart-line"></i><br>
                            تقرير الأداء
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bell"></i> التنبيهات والمهام
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    مرحباً بك في نظام إدارة الأبقار المتطور!
                </div>

                <!-- تحديث سعر الحليب السريع -->
                <div class="card mb-3">
                    <div class="card-header bg-warning text-dark">
                        <i class="fas fa-tint"></i> سعر الحليب الحالي
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-0 text-primary">{{ "%.2f"|format(milk_price) }} د.أ/لتر</h4>
                                <small class="text-muted">السعر الحالي</small>
                            </div>
                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#milkPriceModal">
                                <i class="fas fa-edit"></i> تحديث
                            </button>
                        </div>
                    </div>
                </div>
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        تطعيمات مستحقة
                        <span class="badge bg-warning rounded-pill">0</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        فحوصات دورية
                        <span class="badge bg-info rounded-pill">0</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        ولادات متوقعة
                        <span class="badge bg-success rounded-pill">0</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- الأبقار الحديثة -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-cow"></i> آخر الأبقار المضافة
            </div>
            <div class="card-body">
                <div class="text-center">
                    <p class="text-muted">لا توجد أبقار مسجلة بعد</p>
                    <a href="{{ url_for('add_cattle') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة أول بقرة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تحديث سعر الحليب -->
<div class="modal fade" id="milkPriceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-tint"></i> تحديث سعر الحليب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('update_milk_price') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="current_price_display" class="form-label">السعر الحالي</label>
                        <input type="text" class="form-control" id="current_price_display" value="{{ "%.2f"|format(milk_price) }} د.أ/لتر" readonly>
                    </div>

                    <div class="mb-3">
                        <label for="milk_price" class="form-label">السعر الجديد (د.أ/لتر) *</label>
                        <input type="number" step="0.01" min="0" class="form-control" id="milk_price" name="milk_price" value="{{ milk_price }}" required>
                        <div class="form-text">أدخل السعر الجديد لكل لتر حليب</div>
                    </div>

                    <div class="mb-3">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>تنبيه:</strong> تحديث السعر سيؤثر على حسابات الإيرادات المستقبلية فقط.
                        </div>
                    </div>

                    <!-- معاينة التأثير -->
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-calculator"></i> معاينة التأثير
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-4">
                                    <small class="text-muted">10 لتر</small>
                                    <div id="preview_10" class="fw-bold">{{ "%.2f"|format(milk_price * 10) }} د.أ</div>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">50 لتر</small>
                                    <div id="preview_50" class="fw-bold">{{ "%.2f"|format(milk_price * 50) }} د.أ</div>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">100 لتر</small>
                                    <div id="preview_100" class="fw-bold">{{ "%.2f"|format(milk_price * 100) }} د.أ</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> تحديث السعر
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // تحديث معاينة التأثير عند تغيير السعر
    document.getElementById('milk_price').addEventListener('input', function() {
        const newPrice = parseFloat(this.value) || 0;

        document.getElementById('preview_10').textContent = (newPrice * 10).toFixed(2) + ' د.أ';
        document.getElementById('preview_50').textContent = (newPrice * 50).toFixed(2) + ' د.أ';
        document.getElementById('preview_100').textContent = (newPrice * 100).toFixed(2) + ' د.أ';
    });

    // تحديث سعر الحليب في الوقت الفعلي
    function updateMilkPriceDisplay() {
        fetch('/api/milk-price')
            .then(response => response.json())
            .then(data => {
                if (data.price) {
                    // تحديث العرض في الصفحة الرئيسية
                    const priceElements = document.querySelectorAll('.milk-price-display');
                    priceElements.forEach(element => {
                        element.textContent = data.price.toFixed(2) + ' د.أ/لتر';
                    });
                }
            })
            .catch(error => console.error('خطأ في جلب سعر الحليب:', error));
    }

    // تحديث السعر كل 30 ثانية (اختياري)
    // setInterval(updateMilkPriceDisplay, 30000);
</script>
{% endblock %}
