{% extends "base.html" %}

{% block title %}تعديل البقرة {{ cattle.tag_number }} - نظام إدارة الأبقار{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-edit"></i> تعديل بيانات البقرة: {{ cattle.tag_number }}</h1>
            <a href="{{ url_for('cattle_detail', id=cattle.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> العودة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-cow"></i> تعديل بيانات البقرة
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="tag_number" class="form-label">رقم العلامة *</label>
                            <input type="text" class="form-control" id="tag_number" name="tag_number" 
                                   value="{{ cattle.tag_number }}" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">الاسم</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ cattle.name or '' }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="breed" class="form-label">السلالة</label>
                            <select class="form-select" id="breed" name="breed">
                                <option value="">اختر السلالة</option>
                                <option value="هولشتاين" {{ 'selected' if cattle.breed == 'هولشتاين' else '' }}>هولشتاين</option>
                                <option value="جيرسي" {{ 'selected' if cattle.breed == 'جيرسي' else '' }}>جيرسي</option>
                                <option value="براون سويس" {{ 'selected' if cattle.breed == 'براون سويس' else '' }}>براون سويس</option>
                                <option value="سيمنتال" {{ 'selected' if cattle.breed == 'سيمنتال' else '' }}>سيمنتال</option>
                                <option value="شاروليه" {{ 'selected' if cattle.breed == 'شاروليه' else '' }}>شاروليه</option>
                                <option value="أنجوس" {{ 'selected' if cattle.breed == 'أنجوس' else '' }}>أنجوس</option>
                                <option value="بلدي" {{ 'selected' if cattle.breed == 'بلدي' else '' }}>بلدي</option>
                                <option value="مختلط" {{ 'selected' if cattle.breed == 'مختلط' else '' }}>مختلط</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="gender" class="form-label">الجنس *</label>
                            <select class="form-select" id="gender" name="gender" required>
                                <option value="">اختر الجنس</option>
                                <option value="أنثى" {{ 'selected' if cattle.gender == 'أنثى' else '' }}>أنثى</option>
                                <option value="ذكر" {{ 'selected' if cattle.gender == 'ذكر' else '' }}>ذكر</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="birth_date" class="form-label">تاريخ الولادة</label>
                            <input type="date" class="form-control" id="birth_date" name="birth_date"
                                   value="{{ cattle.birth_date.strftime('%Y-%m-%d') if cattle.birth_date else '' }}">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="weight" class="form-label">الوزن (كغ)</label>
                            <input type="number" step="0.1" class="form-control" id="weight" name="weight"
                                   value="{{ cattle.weight or '' }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="color" class="form-label">اللون</label>
                            <select class="form-select" id="color" name="color">
                                <option value="">اختر اللون</option>
                                <option value="أسود" {{ 'selected' if cattle.color == 'أسود' else '' }}>أسود</option>
                                <option value="أبيض" {{ 'selected' if cattle.color == 'أبيض' else '' }}>أبيض</option>
                                <option value="بني" {{ 'selected' if cattle.color == 'بني' else '' }}>بني</option>
                                <option value="أحمر" {{ 'selected' if cattle.color == 'أحمر' else '' }}>أحمر</option>
                                <option value="أسود وأبيض" {{ 'selected' if cattle.color == 'أسود وأبيض' else '' }}>أسود وأبيض</option>
                                <option value="بني وأبيض" {{ 'selected' if cattle.color == 'بني وأبيض' else '' }}>بني وأبيض</option>
                                <option value="أحمر وأبيض" {{ 'selected' if cattle.color == 'أحمر وأبيض' else '' }}>أحمر وأبيض</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="نشط" {{ 'selected' if cattle.status == 'نشط' else '' }}>نشط</option>
                                <option value="مريض" {{ 'selected' if cattle.status == 'مريض' else '' }}>مريض</option>
                                <option value="حامل" {{ 'selected' if cattle.status == 'حامل' else '' }}>حامل</option>
                                <option value="مباع" {{ 'selected' if cattle.status == 'مباع' else '' }}>مباع</option>
                                <option value="نافق" {{ 'selected' if cattle.status == 'نافق' else '' }}>نافق</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="purchase_price" class="form-label">سعر الشراء (دينار أردني)</label>
                            <input type="number" step="0.01" class="form-control" id="purchase_price" name="purchase_price"
                                   value="{{ cattle.purchase_price or '' }}">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="purchase_date" class="form-label">تاريخ الشراء</label>
                            <input type="date" class="form-control" id="purchase_date" name="purchase_date"
                                   value="{{ cattle.purchase_date.strftime('%Y-%m-%d') if cattle.purchase_date else '' }}">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3">{{ cattle.notes or '' }}</textarea>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('cattle_detail', id=cattle.id) }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
