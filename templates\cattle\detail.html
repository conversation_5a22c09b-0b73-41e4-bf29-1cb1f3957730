{% extends "base.html" %}

{% block title %}تفاصيل البقرة {{ cattle.tag_number }} - نظام إدارة الأبقار{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-cow"></i> تفاصيل البقرة: {{ cattle.tag_number }}</h1>
            <div>
                <a href="{{ url_for('edit_cattle', id=cattle.id) }}" class="btn btn-warning me-2">
                    <i class="fas fa-edit"></i> تعديل
                </a>
                <button class="btn btn-danger me-2" onclick="confirmDelete({{ cattle.id }}, '{{ cattle.tag_number }}')">
                    <i class="fas fa-trash"></i> حذف
                </button>
                <a href="{{ url_for('cattle_list') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- معلومات أساسية -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> المعلومات الأساسية
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>رقم العلامة:</strong></td>
                        <td>{{ cattle.tag_number }}</td>
                    </tr>
                    <tr>
                        <td><strong>الاسم:</strong></td>
                        <td>{{ cattle.name or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td><strong>السلالة:</strong></td>
                        <td>{{ cattle.breed or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td><strong>الجنس:</strong></td>
                        <td>
                            <span class="gender-text" style="color:#000 !important; font-weight:700 !important; opacity:1 !important;">
                                {{ cattle.gender if cattle.gender and cattle.gender|trim != '' else 'غير محدد' }}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>تاريخ الولادة:</strong></td>
                        <td>{{ cattle.birth_date.strftime('%Y/%m/%d') if cattle.birth_date else 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td><strong>العمر:</strong></td>
                        <td>
                            {% if cattle.birth_date %}
                                {{ cattle.birth_date | calculate_age }} سنة
                            {% else %}
                                غير محدد
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>الوزن:</strong></td>
                        <td>{{ cattle.weight }} كغ</td>
                    </tr>
                    <tr>
                        <td><strong>اللون:</strong></td>
                        <td>{{ cattle.color or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td><strong>الحالة:</strong></td>
                        <td>
                            {% if cattle.status == 'نشط' %}
                                <span class="badge bg-success">{{ cattle.status }}</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ cattle.status }}</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-dollar-sign"></i> المعلومات المالية
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>سعر الشراء:</strong></td>
                        <td class="currency">
                            {% if cattle.purchase_price %}
                                {{ "%.2f"|format(cattle.purchase_price) }} دينار أردني
                            {% else %}
                                غير محدد
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>تاريخ الشراء:</strong></td>
                        <td>{{ cattle.purchase_date.strftime('%Y/%m/%d') if cattle.purchase_date else 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td><strong>القيمة الحالية:</strong></td>
                        <td class="currency">قيد التقدير</td>
                    </tr>
                </table>
                
                <div class="mt-3">
                    <a href="{{ url_for('add_financial_transaction') }}" class="btn btn-success btn-sm w-100">
                        <i class="fas fa-plus"></i> إضافة معاملة مالية
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الإجراءات السريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-tools"></i> الإجراءات السريعة
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('add_milk_record', cattle_id=cattle.id) }}" class="btn btn-info w-100">
                            <i class="fas fa-tint"></i><br>
                            تسجيل إنتاج حليب
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('add_health_record', cattle_id=cattle.id) }}" class="btn btn-warning w-100">
                            <i class="fas fa-syringe"></i><br>
                            إضافة سجل صحي
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('add_breeding_record', cattle_id=cattle.id) }}" class="btn btn-success w-100">
                            <i class="fas fa-heart"></i><br>
                            تسجيل تكاثر
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('add_health_record', cattle_id=cattle.id) }}" class="btn btn-danger w-100">
                            <i class="fas fa-stethoscope"></i><br>
                            فحص طبي
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- السجلات الصحية -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-heartbeat"></i> السجلات الصحية
            </div>
            <div class="card-body">
                {% if health_records %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>النوع</th>
                                    <th>الوصف</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in health_records[:5] %}
                                <tr>
                                    <td>{{ record.date.strftime('%Y/%m/%d') if record.date else '-' }}</td>
                                    <td>{{ record.record_type }}</td>
                                    <td>{{ record.description[:30] }}...</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center">لا توجد سجلات صحية</p>
                {% endif %}
                <a href="{{ url_for('health_records') }}" class="btn btn-outline-primary btn-sm w-100">
                    عرض جميع السجلات
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-tint"></i> إنتاج الحليب الأخير
            </div>
            <div class="card-body">
                {% if milk_records %}
                    <div class="table-responsive">
                        <table class="table table-sm" id="cattleMilkTable">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الصباح</th>
                                    <th>المساء</th>
                                    <th>الإجمالي</th>
                                    <th>القيمة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in milk_records[:5] %}
                                <tr>
                                    <td>{{ record.date.strftime('%Y/%m/%d') if record.date else '-' }}</td>
                                    <td>{{ record.morning_amount }} لتر</td>
                                    <td>{{ record.evening_amount }} لتر</td>
                                    <td><strong>{{ record.total_amount }} لتر</strong></td>
                                    <td class="currency">{{ "%.2f"|format(record.total_amount * milk_price) }} د.أ</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot class="table-light">
                                <tr class="fw-bold">
                                    <td colspan="3" class="text-end">الإجمالي:</td>
                                    <td id="cattleTotalQuantity">{{ "%.1f"|format(milk_records[:5]|sum(attribute='total_amount')) }} لتر</td>
                                    <td class="currency" id="cattleTotalValue">{{ "%.2f"|format((milk_records[:5]|sum(attribute='total_amount')) * milk_price) }} د.أ</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center">لا توجد سجلات إنتاج</p>
                {% endif %}
                <a href="{{ url_for('milk_production') }}" class="btn btn-outline-info btn-sm w-100">
                    عرض جميع السجلات
                </a>
            </div>
        </div>
    </div>
</div>

{% if cattle.notes %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-sticky-note"></i> ملاحظات
            </div>
            <div class="card-body">
                <p>{{ cattle.notes }}</p>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
    // تأكيد الحذف
    function confirmDelete(cattleId, tagNumber) {
        if (confirm('هل أنت متأكد من حذف البقرة ' + tagNumber + '؟\nسيتم حذف جميع السجلات المرتبطة بها!')) {
            // إنشاء نموذج مخفي لإرسال طلب الحذف
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/cattle/delete/' + cattleId;
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
{% endblock %}
