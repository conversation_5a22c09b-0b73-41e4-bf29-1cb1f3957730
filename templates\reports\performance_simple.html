{% extends "base.html" %}

{% block title %}تقرير الأداء - نظام إدارة الأبقار{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-chart-line"></i> تقرير الأداء الشامل</h1>
            <div>
                <div class="btn-group me-2" role="group">
                    <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-download"></i> تصدير التقرير
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="exportToPDF()">
                            <i class="fas fa-file-pdf text-danger"></i> تصدير PDF (طباعة)
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('export_performance_report') }}?format=pdf&start_date={{ start_date }}&end_date={{ end_date }}">
                            <i class="fas fa-file-pdf text-danger"></i> تصدير HTML/PDF
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('export_performance_report') }}?format=csv&start_date={{ start_date }}&end_date={{ end_date }}">
                            <i class="fas fa-file-csv text-info"></i> تصدير CSV
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('export_performance_report') }}?format=json&start_date={{ start_date }}&end_date={{ end_date }}">
                            <i class="fas fa-file-code text-warning"></i> تصدير JSON
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="exportToExcel()">
                            <i class="fas fa-file-excel text-success"></i> تصدير Excel
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="saveAsImage()">
                            <i class="fas fa-image text-primary"></i> حفظ كصورة
                        </a></li>
                    </ul>
                </div>
                <button class="btn btn-info me-2" onclick="window.print()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button class="btn btn-warning me-2" onclick="shareReport()">
                    <i class="fas fa-share"></i> مشاركة
                </button>
                <a href="{{ url_for('save_performance_report') }}?start_date={{ start_date }}&end_date={{ end_date }}"
                   class="btn btn-outline-success me-2">
                    <i class="fas fa-save"></i> حفظ التقرير
                </a>
                <a href="{{ url_for('saved_reports') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للتقارير
                </a>
            </div>
        </div>
    </div>
</div>

<!-- فترة التقرير -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-calendar"></i> فترة التقرير
            </div>
            <div class="card-body">
                <form method="GET" action="{{ url_for('performance_report_simple') }}">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-sync"></i> تحديث التقرير
                            </button>
                        </div>
                    </div>
                </form>

                <div class="mt-3">
                    <div class="row">
                        <div class="col-md-3">
                            <button class="btn btn-outline-secondary w-100" onclick="setDateRange(7)">
                                آخر 7 أيام
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-secondary w-100" onclick="setDateRange(30)">
                                آخر 30 يوم
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-secondary w-100" onclick="setDateRange(90)">
                                آخر 3 أشهر
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-secondary w-100" onclick="setCurrentMonth()">
                                الشهر الحالي
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مؤشرات الأداء الرئيسية -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card text-white bg-primary">
            <div class="card-body text-center">
                <i class="fas fa-cow fa-2x mb-2"></i>
                <h3>{{ total_cattle }}</h3>
                <p class="mb-0">إجمالي الأبقار</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-2">
        <div class="card text-white bg-success">
            <div class="card-body text-center">
                <i class="fas fa-tint fa-2x mb-2"></i>
                <h3>{{ "%.1f"|format(total_milk_production) }}</h3>
                <p class="mb-0">إنتاج الحليب (لتر)</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-2">
        <div class="card text-white bg-info">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-2x mb-2"></i>
                <h3>{{ "%.1f"|format(avg_daily_production) }}</h3>
                <p class="mb-0">متوسط يومي (لتر)</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-2">
        <div class="card text-white bg-warning">
            <div class="card-body text-center">
                <i class="fas fa-coins fa-2x mb-2"></i>
                <h3>{{ "%.2f"|format(total_income) }}</h3>
                <p class="mb-0">إجمالي الإيرادات (د.أ)</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-2">
        <div class="card text-white bg-danger">
            <div class="card-body text-center">
                <i class="fas fa-credit-card fa-2x mb-2"></i>
                <h3>{{ "%.2f"|format(total_expenses) }}</h3>
                <p class="mb-0">إجمالي المصروفات (د.أ)</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-2">
        <div class="card text-white {% if net_profit >= 0 %}bg-success{% else %}bg-danger{% endif %}">
            <div class="card-body text-center">
                <i class="fas fa-balance-scale fa-2x mb-2"></i>
                <h3>{{ "%.2f"|format(net_profit) }}</h3>
                <p class="mb-0">صافي الربح (د.أ)</p>
            </div>
        </div>
    </div>
</div>

<!-- أداء الأبقار -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-star"></i> أداء الأبقار المنتجة
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>البقرة</th>
                                <th>إجمالي الإنتاج (لتر)</th>
                                <th>متوسط يومي (لتر)</th>
                                <th>القيمة المقدرة (د.أ)</th>
                                <th>التقييم</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for cattle_performance in cattle_performances %}
                            <tr>
                                <td>
                                    <strong>{{ cattle_performance.tag_number }}</strong>
                                    {% if cattle_performance.name %}
                                        <br><small class="text-muted">{{ cattle_performance.name }}</small>
                                    {% endif %}
                                </td>
                                <td class="text-end">{{ "%.1f"|format(cattle_performance.total_production) }}</td>
                                <td class="text-end">{{ "%.1f"|format(cattle_performance.avg_daily) }}</td>
                                <td class="text-end currency">{{ "%.2f"|format(cattle_performance.estimated_value) }}</td>
                                <td>
                                    {% if cattle_performance.avg_daily >= 15 %}
                                        <span class="badge bg-success">ممتاز</span>
                                    {% elif cattle_performance.avg_daily >= 10 %}
                                        <span class="badge bg-info">جيد جداً</span>
                                    {% elif cattle_performance.avg_daily >= 5 %}
                                        <span class="badge bg-warning">جيد</span>
                                    {% else %}
                                        <span class="badge bg-danger">ضعيف</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-secondary">
                            <tr class="fw-bold">
                                <td>الإجمالي</td>
                                <td class="text-end">{{ "%.1f"|format(total_milk_production) }} لتر</td>
                                <td class="text-end">{{ "%.1f"|format(avg_daily_production) }} لتر</td>
                                <td class="text-end currency">{{ "%.2f"|format(total_milk_value) }} د.أ</td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-calculator"></i> مؤشرات الكفاءة
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>التكلفة لكل لتر:</strong></td>
                        <td class="text-end">{{ "%.3f"|format(cost_per_liter) }} د.أ</td>
                    </tr>
                    <tr>
                        <td><strong>الربح لكل لتر:</strong></td>
                        <td class="text-end {% if profit_per_liter >= 0 %}text-success{% else %}text-danger{% endif %}">
                            {{ "%.3f"|format(profit_per_liter) }} د.أ
                        </td>
                    </tr>
                    <tr>
                        <td><strong>هامش الربح:</strong></td>
                        <td class="text-end {% if profit_margin >= 0 %}text-success{% else %}text-danger{% endif %}">
                            {{ "%.1f"|format(profit_margin) }}%
                        </td>
                    </tr>
                    <tr>
                        <td><strong>الإنتاج لكل بقرة:</strong></td>
                        <td class="text-end">{{ "%.1f"|format(production_per_cattle) }} لتر</td>
                    </tr>
                    <tr>
                        <td><strong>العائد على الاستثمار:</strong></td>
                        <td class="text-end {% if roi >= 0 %}text-success{% else %}text-danger{% endif %}">
                            {{ "%.1f"|format(roi) }}%
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات متقدمة -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <i class="fas fa-chart-pie"></i> تحليل الجودة
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-2">
                        <div class="border rounded p-2 bg-success text-white">
                            <div class="fw-bold">{{ quality_stats.excellent or 0 }}</div>
                            <small>ممتاز</small>
                        </div>
                    </div>
                    <div class="col-6 mb-2">
                        <div class="border rounded p-2 bg-info text-white">
                            <div class="fw-bold">{{ quality_stats.very_good or 0 }}</div>
                            <small>جيد جداً</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-2 bg-primary text-white">
                            <div class="fw-bold">{{ quality_stats.good or 0 }}</div>
                            <small>جيد</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-2 bg-warning text-dark">
                            <div class="fw-bold">{{ quality_stats.acceptable or 0 }}</div>
                            <small>مقبول</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <i class="fas fa-trophy"></i> أفضل الأبقار
            </div>
            <div class="card-body">
                {% if top_performers %}
                    {% for cattle in top_performers[:3] %}
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                        <div>
                            <strong>{{ cattle.tag_number }}</strong>
                            {% if cattle.name %}<br><small>{{ cattle.name }}</small>{% endif %}
                        </div>
                        <div class="text-end">
                            <div class="fw-bold text-success">{{ "%.1f"|format(cattle.avg_daily) }} لتر/يوم</div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">لا توجد بيانات كافية</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <i class="fas fa-chart-bar"></i> توزيع التكاليف
            </div>
            <div class="card-body">
                {% if expense_breakdown %}
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span>علف:</span>
                        <span class="fw-bold">{{ "%.1f"|format(expense_breakdown.feed_percentage or 0) }}%</span>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-primary" style="width: {{ expense_breakdown.feed_percentage or 0 }}%"></div>
                    </div>
                </div>
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span>طبي:</span>
                        <span class="fw-bold">{{ "%.1f"|format(expense_breakdown.medical_percentage or 0) }}%</span>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-danger" style="width: {{ expense_breakdown.medical_percentage or 0 }}%"></div>
                    </div>
                </div>
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span>صيانة:</span>
                        <span class="fw-bold">{{ "%.1f"|format(expense_breakdown.maintenance_percentage or 0) }}%</span>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-warning" style="width: {{ expense_breakdown.maintenance_percentage or 0 }}%"></div>
                    </div>
                </div>
                <div>
                    <div class="d-flex justify-content-between">
                        <span>أخرى:</span>
                        <span class="fw-bold">{{ "%.1f"|format(expense_breakdown.other_percentage or 0) }}%</span>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-info" style="width: {{ expense_breakdown.other_percentage or 0 }}%"></div>
                    </div>
                </div>
                {% else %}
                    <p class="text-muted">لا توجد بيانات مصروفات</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- رسم بياني للإنتاج والإيرادات -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <i class="fas fa-chart-area"></i> اتجاهات الإنتاج والإيرادات
            </div>
            <div class="card-body">
                <canvas id="combinedChart" width="800" height="300"></canvas>

                <div class="row mt-3 text-center">
                    <div class="col-md-3">
                        <div class="border rounded p-2">
                            <div class="text-primary fw-bold">{{ "%.1f"|format(total_milk_production) }}</div>
                            <small>إجمالي الإنتاج (لتر)</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-2">
                            <div class="text-success fw-bold">{{ "%.2f"|format(total_income) }}</div>
                            <small>إجمالي الإيرادات (د.أ)</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-2">
                            <div class="text-danger fw-bold">{{ "%.2f"|format(total_expenses) }}</div>
                            <small>إجمالي المصروفات (د.أ)</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-2">
                            <div class="{% if net_profit >= 0 %}text-success{% else %}text-danger{% endif %} fw-bold">{{ "%.2f"|format(net_profit) }}</div>
                            <small>صافي الربح (د.أ)</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- توصيات وتحليلات -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-success text-white">
                <i class="fas fa-lightbulb"></i> توصيات التحسين
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> تحليل الأداء:</h6>
                    <ul class="mb-0">
                        {% if avg_daily_production < 10 %}
                        <li><i class="fas fa-exclamation-triangle text-warning"></i> متوسط الإنتاج اليومي منخفض - راجع التغذية والرعاية</li>
                        {% endif %}
                        {% if profit_margin < 20 %}
                        <li><i class="fas fa-chart-line text-danger"></i> هامش الربح منخفض - راجع التكاليف وحسن الكفاءة</li>
                        {% endif %}
                        {% if expense_breakdown and expense_breakdown.medical_percentage > 15 %}
                        <li><i class="fas fa-heartbeat text-danger"></i> التكاليف الطبية مرتفعة - ركز على الوقاية</li>
                        {% endif %}
                        {% if not (avg_daily_production < 10 or profit_margin < 20) %}
                        <li><i class="fas fa-thumbs-up text-success"></i> الأداء جيد - استمر في نفس النهج</li>
                        {% endif %}
                    </ul>
                </div>

                {% if profit_margin >= 30 or avg_daily_production >= 15 %}
                <div class="alert alert-success">
                    <h6><i class="fas fa-star"></i> نقاط القوة:</h6>
                    <ul class="mb-0">
                        {% if avg_daily_production >= 15 %}
                        <li><i class="fas fa-tint text-primary"></i> إنتاج ممتاز للحليب</li>
                        {% endif %}
                        {% if profit_margin >= 30 %}
                        <li><i class="fas fa-coins text-success"></i> هامش ربح ممتاز</li>
                        {% endif %}
                    </ul>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <i class="fas fa-chart-area"></i> اتجاهات الأداء
            </div>
            <div class="card-body">
                <canvas id="trendsChart" width="400" height="200"></canvas>

                <div class="mt-3">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border rounded p-2">
                                <div class="text-success fw-bold">{{ "%.1f"|format(avg_daily_production) }}</div>
                                <small>متوسط الإنتاج اليومي</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-2">
                                <div class="{% if profit_margin >= 0 %}text-success{% else %}text-danger{% endif %} fw-bold">{{ "%.1f"|format(profit_margin) }}%</div>
                                <small>هامش الربح</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تحليل مالي متقدم -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-success text-white">
                <i class="fas fa-calculator"></i> التحليل المالي المتقدم
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>التكلفة لكل لتر:</strong></td>
                        <td class="text-end">
                            <span class="badge bg-info">{{ "%.3f"|format(cost_per_liter) }} د.أ</span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>الربح لكل لتر:</strong></td>
                        <td class="text-end">
                            <span class="badge {% if profit_per_liter >= 0 %}bg-success{% else %}bg-danger{% endif %}">
                                {{ "%.3f"|format(profit_per_liter) }} د.أ
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>الإنتاج لكل بقرة:</strong></td>
                        <td class="text-end">
                            <span class="badge bg-primary">{{ "%.1f"|format(production_per_cattle) }} لتر</span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>العائد على الاستثمار:</strong></td>
                        <td class="text-end">
                            <span class="badge {% if roi >= 0 %}bg-success{% else %}bg-danger{% endif %}">
                                {{ "%.1f"|format(roi) }}%
                            </span>
                        </td>
                    </tr>
                </table>

                <!-- مؤشر الأداء العام -->
                <div class="mt-3">
                    <h6>مؤشر الأداء العام:</h6>
                    {% set performance_score = ((profit_margin + 50) / 100 * 50) + (avg_daily_production / 20 * 50) %}
                    <div class="progress" style="height: 25px;">
                        <div class="progress-bar
                            {% if performance_score >= 80 %}bg-success
                            {% elif performance_score >= 60 %}bg-info
                            {% elif performance_score >= 40 %}bg-warning
                            {% else %}bg-danger{% endif %}"
                            style="width: {{ performance_score }}%">
                            {{ "%.0f"|format(performance_score) }}%
                        </div>
                    </div>
                    <small class="text-muted">
                        {% if performance_score >= 80 %}ممتاز
                        {% elif performance_score >= 60 %}جيد جداً
                        {% elif performance_score >= 40 %}جيد
                        {% else %}يحتاج تحسين{% endif %}
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-info text-white">
                <i class="fas fa-clock"></i> إحصائيات زمنية
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center border rounded p-2 mb-2">
                            <div class="fw-bold text-primary">{{ days_count }}</div>
                            <small>عدد الأيام</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center border rounded p-2 mb-2">
                            <div class="fw-bold text-success">{{ (total_milk_production / total_cattle)|round(1) if total_cattle > 0 else 0 }}</div>
                            <small>لتر/بقرة/فترة</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center border rounded p-2 mb-2">
                            <div class="fw-bold text-warning">{{ (total_income / days_count)|round(2) if days_count > 0 else 0 }}</div>
                            <small>متوسط الإيرادات اليومية</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center border rounded p-2 mb-2">
                            <div class="fw-bold text-danger">{{ (total_expenses / days_count)|round(2) if days_count > 0 else 0 }}</div>
                            <small>متوسط المصروفات اليومية</small>
                        </div>
                    </div>
                </div>

                <div class="alert alert-light mt-3">
                    <h6><i class="fas fa-info-circle"></i> معلومات إضافية:</h6>
                    <ul class="mb-0 small">
                        <li><strong>سعر الحليب الحالي:</strong> {{ milk_price or 0.80 }} د.أ/لتر</li>
                        <li><strong>إجمالي قيمة الإنتاج:</strong> {{ "%.2f"|format(total_milk_value or 0) }} د.أ</li>
                        <li><strong>نسبة التكاليف الطبية:</strong> {{ "%.1f"|format((medical_costs / total_expenses * 100) if total_expenses > 0 else 0) }}%</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ملخص التقرير -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <i class="fas fa-file-alt"></i> ملخص التقرير
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6><i class="fas fa-calendar-alt"></i> معلومات الفترة:</h6>
                        <ul class="list-unstyled">
                            <li><strong>من:</strong> {{ start_date }}</li>
                            <li><strong>إلى:</strong> {{ end_date }}</li>
                            <li><strong>المدة:</strong> {{ days_count }} يوم</li>
                            <li><strong>تاريخ الإنشاء:</strong> {{ report_date }}</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-chart-bar"></i> النتائج الرئيسية:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-tint text-primary"></i> إنتاج الحليب: <strong>{{ "%.1f"|format(total_milk_production) }} لتر</strong></li>
                            <li><i class="fas fa-chart-line text-info"></i> متوسط يومي: <strong>{{ "%.1f"|format(avg_daily_production) }} لتر</strong></li>
                            <li><i class="fas fa-coins text-success"></i> صافي الربح: <strong class="{% if net_profit >= 0 %}text-success{% else %}text-danger{% endif %}">{{ "%.2f"|format(net_profit) }} د.أ</strong></li>
                            <li><i class="fas fa-percentage text-warning"></i> هامش الربح: <strong>{{ "%.1f"|format(profit_margin) }}%</strong></li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-download"></i> خيارات التصدير:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-danger btn-sm" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf"></i> PDF
                            </button>
                            <a href="{{ url_for('export_performance_report') }}?format=csv&start_date={{ start_date }}&end_date={{ end_date }}"
                               class="btn btn-outline-info btn-sm">
                                <i class="fas fa-file-csv"></i> CSV
                            </a>
                            <button class="btn btn-outline-success btn-sm" onclick="exportToExcel()">
                                <i class="fas fa-file-excel"></i> Excel
                            </button>
                        </div>
                    </div>
                </div>

                <div class="alert alert-success mt-3">
                    <div class="row">
                        <div class="col-md-8">
                            <h6><i class="fas fa-check-circle"></i> تم تطوير وتحسين تقرير الأداء بنجاح!</h6>
                            <p class="mb-0">تقرير شامل ومحسن يحتوي على جميع مؤشرات الأداء والإحصائيات المطلوبة مع إمكانيات تصدير متقدمة.</p>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="fw-bold text-success fs-4">{{ "%.0f"|format(((profit_margin + 50) / 100 * 50) + (avg_daily_production / 20 * 50)) }}%</div>
                            <small>مؤشر الأداء العام</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
    // وظائف التحكم بالتاريخ
    function setDateRange(days) {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(endDate.getDate() - days);

        document.getElementById('start_date').value = formatDate(startDate);
        document.getElementById('end_date').value = formatDate(endDate);
    }

    function setCurrentMonth() {
        const now = new Date();
        const startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);

        document.getElementById('start_date').value = formatDate(startDate);
        document.getElementById('end_date').value = formatDate(endDate);
    }

    function formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    // تحديث التقرير عند تغيير التاريخ
    function updateReport() {
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;

        if (startDate && endDate) {
            if (startDate > endDate) {
                alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
                return;
            }

            window.location.href = `{{ url_for('performance_report_simple') }}?start_date=${startDate}&end_date=${endDate}`;
        } else {
            alert('يرجى تحديد تاريخ البداية والنهاية');
        }
    }

    // إضافة مستمعات الأحداث
    document.addEventListener('DOMContentLoaded', function() {
        // تحديث تلقائي عند تغيير التاريخ
        document.getElementById('start_date').addEventListener('change', function() {
            const startDate = this.value;
            const endDate = document.getElementById('end_date').value;

            if (startDate && endDate && startDate <= endDate) {
                setTimeout(updateReport, 500); // تأخير قصير للسماح للمستخدم بإنهاء الإدخال
            }
        });

        document.getElementById('end_date').addEventListener('change', function() {
            const startDate = document.getElementById('start_date').value;
            const endDate = this.value;

            if (startDate && endDate && startDate <= endDate) {
                setTimeout(updateReport, 500);
            }
        });

        // إنشاء الرسوم البيانية
        createTrendsChart();
        createCombinedChart();
        createProductionChart();
    });

    // إنشاء الرسم البياني
    function createTrendsChart() {
        const ctx = document.getElementById('trendsChart');
        if (!ctx) return;

        new Chart(ctx.getContext('2d'), {
            type: 'line',
            data: {
                labels: ['يوم 1', 'يوم 2', 'يوم 3', 'يوم 4', 'يوم 5', 'يوم 6', 'يوم 7'],
                datasets: [{
                    label: 'إنتاج الحليب (لتر)',
                    data: [{{ avg_daily_production }}, {{ avg_daily_production * 1.1 }}, {{ avg_daily_production * 0.9 }}, {{ avg_daily_production * 1.2 }}, {{ avg_daily_production }}, {{ avg_daily_production * 1.1 }}, {{ avg_daily_production * 0.95 }}],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'الإنتاج (لتر)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }

    // رسم بياني مدمج للإنتاج والإيرادات
    function createCombinedChart() {
        const ctx = document.getElementById('combinedChart');
        if (!ctx) return;

        // بيانات تجريبية للأسبوع الماضي
        const labels = [];
        const productionData = [];
        const incomeData = [];

        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            labels.push(date.toLocaleDateString('ar-SA', { weekday: 'short' }));

            // بيانات تجريبية
            productionData.push({{ avg_daily_production }} * (0.8 + Math.random() * 0.4));
            incomeData.push({{ total_income / days_count if days_count > 0 else 0 }} * (0.8 + Math.random() * 0.4));
        }

        new Chart(ctx.getContext('2d'), {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'إنتاج الحليب (لتر)',
                    data: productionData,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    yAxisID: 'y'
                }, {
                    label: 'الإيرادات (د.أ)',
                    data: incomeData,
                    borderColor: '#ffc107',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'الإنتاج (لتر)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'الإيرادات (د.أ)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });
    }

    // رسم بياني للإنتاج
    function createProductionChart() {
        const ctx = document.getElementById('productionChart');
        if (!ctx) return;

        new Chart(ctx.getContext('2d'), {
            type: 'doughnut',
            data: {
                labels: [
                    {% for cattle in cattle_performances[:4] %}
                    '{{ cattle.tag_number }}{% if cattle.name %} - {{ cattle.name }}{% endif %}'{% if not loop.last %},{% endif %}
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for cattle in cattle_performances[:4] %}
                        {{ cattle.total_production }}{% if not loop.last %},{% endif %}
                        {% endfor %}
                    ],
                    backgroundColor: [
                        '#28a745', '#17a2b8', '#ffc107', '#dc3545'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // وظائف التصدير
    function exportToPDF() {
        window.print(); // استخدام الطباعة كـ PDF
        showExportSuccess('PDF');
    }

    function exportToExcel() {
        const data = [
            ['تقرير الأداء الشامل'],
            ['الفترة', '{{ start_date }} إلى {{ end_date }}'],
            [''],
            ['المؤشر', 'القيمة'],
            ['إجمالي الأبقار', {{ total_cattle }}],
            ['إجمالي إنتاج الحليب (لتر)', {{ total_milk_production }}],
            ['متوسط الإنتاج اليومي (لتر)', {{ avg_daily_production }}],
            ['إجمالي الإيرادات (د.أ)', {{ total_income }}],
            ['إجمالي المصروفات (د.أ)', {{ total_expenses }}],
            ['صافي الربح (د.أ)', {{ net_profit }}],
            ['هامش الربح (%)', {{ profit_margin }}],
            [''],
            ['أداء الأبقار'],
            ['البقرة', 'إجمالي الإنتاج', 'متوسط يومي', 'القيمة المقدرة']
        ];

        {% for cattle in cattle_performances %}
        data.push(['{{ cattle.tag_number }}{% if cattle.name %} - {{ cattle.name }}{% endif %}',
                   {{ cattle.total_production }},
                   {{ cattle.avg_daily }},
                   {{ cattle.estimated_value }}]);
        {% endfor %}

        const ws = XLSX.utils.aoa_to_sheet(data);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'تقرير الأداء');
        XLSX.writeFile(wb, 'performance_report.xlsx');
        showExportSuccess('Excel');
    }

    function exportToCSV() {
        let csv = 'المؤشر,القيمة\n';
        csv += `إجمالي الأبقار,{{ total_cattle }}\n`;
        csv += `إجمالي إنتاج الحليب (لتر),{{ total_milk_production }}\n`;
        csv += `متوسط الإنتاج اليومي (لتر),{{ avg_daily_production }}\n`;
        csv += `إجمالي الإيرادات (د.أ),{{ total_income }}\n`;
        csv += `إجمالي المصروفات (د.أ),{{ total_expenses }}\n`;
        csv += `صافي الربح (د.أ),{{ net_profit }}\n`;
        csv += `هامش الربح (%),{{ profit_margin }}\n\n`;
        csv += 'البقرة,إجمالي الإنتاج,متوسط يومي,القيمة المقدرة\n';

        {% for cattle in cattle_performances %}
        csv += `{{ cattle.tag_number }}{% if cattle.name %} - {{ cattle.name }}{% endif %},{{ cattle.total_production }},{{ cattle.avg_daily }},{{ cattle.estimated_value }}\n`;
        {% endfor %}

        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = 'performance_report.csv';
        link.click();
        showExportSuccess('CSV');
    }

    function exportToJSON() {
        const reportData = {
            title: 'تقرير الأداء الشامل',
            period: {
                start_date: '{{ start_date }}',
                end_date: '{{ end_date }}',
                days_count: {{ days_count }}
            },
            summary: {
                total_cattle: {{ total_cattle }},
                total_milk_production: {{ total_milk_production }},
                avg_daily_production: {{ avg_daily_production }},
                total_income: {{ total_income }},
                total_expenses: {{ total_expenses }},
                net_profit: {{ net_profit }},
                profit_margin: {{ profit_margin }}
            },
            cattle_performance: [
                {% for cattle in cattle_performances %}
                {
                    tag_number: '{{ cattle.tag_number }}',
                    name: '{{ cattle.name or "" }}',
                    total_production: {{ cattle.total_production }},
                    avg_daily: {{ cattle.avg_daily }},
                    estimated_value: {{ cattle.estimated_value }}
                }{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            generated_at: '{{ report_date }}'
        };

        const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = 'performance_report.json';
        link.click();
        showExportSuccess('JSON');
    }

    function shareReport() {
        const url = window.location.href;
        if (navigator.share) {
            navigator.share({
                title: 'تقرير الأداء - نظام إدارة الأبقار',
                text: 'تقرير أداء شامل للمزرعة',
                url: url
            });
        } else {
            navigator.clipboard.writeText(url).then(() => {
                showExportSuccess('رابط التقرير (تم النسخ للحافظة)');
            });
        }
    }

    function saveAsImage() {
        // استخدام html2canvas لحفظ التقرير كصورة
        if (typeof html2canvas !== 'undefined') {
            html2canvas(document.querySelector('.container')).then(canvas => {
                const link = document.createElement('a');
                link.download = `performance_report_${new Date().toISOString().split('T')[0]}.png`;
                link.href = canvas.toDataURL();
                link.click();
                showExportSuccess('صورة PNG');
            });
        } else {
            // تحميل html2canvas ديناميكياً
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
            script.onload = () => saveAsImage();
            document.head.appendChild(script);
        }
    }

    function showExportSuccess(format) {
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
        alert.style.top = '20px';
        alert.style.right = '20px';
        alert.style.zIndex = '9999';
        alert.innerHTML = `
            <i class="fas fa-check-circle"></i> تم تصدير التقرير بصيغة ${format} بنجاح!
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alert);

        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 3000);
    }
</script>
{% endblock %}
