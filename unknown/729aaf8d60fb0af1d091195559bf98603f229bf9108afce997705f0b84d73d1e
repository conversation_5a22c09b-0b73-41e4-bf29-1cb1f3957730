{% extends "base.html" %}

{% block title %}تعديل المادة العلفية - {{ ingredient.name }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-edit"></i> تعديل المادة العلفية: {{ ingredient.name }}</h1>
            <a href="{{ url_for('feed_ingredients') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-wheat-awn"></i> تعديل بيانات المادة العلفية
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم المادة العلفية *</label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ ingredient.name }}" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label">فئة المادة</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">اختر الفئة</option>
                                <option value="حبوب" {{ 'selected' if ingredient.category == 'حبوب' }}>حبوب</option>
                                <option value="بروتين" {{ 'selected' if ingredient.category == 'بروتين' }}>مصادر البروتين</option>
                                <option value="ألياف" {{ 'selected' if ingredient.category == 'ألياف' }}>مصادر الألياف</option>
                                <option value="فيتامينات" {{ 'selected' if ingredient.category == 'فيتامينات' }}>فيتامينات ومعادن</option>
                                <option value="دهون" {{ 'selected' if ingredient.category == 'دهون' }}>مصادر الدهون</option>
                                <option value="أخرى" {{ 'selected' if ingredient.category == 'أخرى' }}>أخرى</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="protein_percentage" class="form-label">نسبة البروتين %</label>
                            <input type="number" step="0.1" class="form-control" id="protein_percentage" name="protein_percentage" value="{{ ingredient.protein_percentage or '' }}">
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="energy_value" class="form-label">القيمة الطاقية (MJ/kg)</label>
                            <input type="number" step="0.1" class="form-control" id="energy_value" name="energy_value" value="{{ ingredient.energy_value or '' }}">
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="fiber_percentage" class="form-label">نسبة الألياف %</label>
                            <input type="number" step="0.1" class="form-control" id="fiber_percentage" name="fiber_percentage" value="{{ ingredient.fiber_percentage or '' }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="fat_percentage" class="form-label">نسبة الدهون %</label>
                            <input type="number" step="0.1" class="form-control" id="fat_percentage" name="fat_percentage" value="{{ ingredient.fat_percentage or '' }}">
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="moisture_percentage" class="form-label">نسبة الرطوبة %</label>
                            <input type="number" step="0.1" class="form-control" id="moisture_percentage" name="moisture_percentage" value="{{ ingredient.moisture_percentage or '' }}">
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="price_per_kg" class="form-label">السعر/كغ (د.أ)</label>
                            <input type="number" step="0.01" class="form-control" id="price_per_kg" name="price_per_kg" value="{{ ingredient.price_per_kg or '' }}">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="supplier" class="form-label">المورد</label>
                        <input type="text" class="form-control" id="supplier" name="supplier" value="{{ ingredient.supplier or '' }}">
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3">{{ ingredient.notes or '' }}</textarea>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('feed_ingredients') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ التعديلات
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- معلومات الاستخدام -->
        <div class="card mt-4">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> معلومات الاستخدام
            </div>
            <div class="card-body">
                {% set usage_count = ingredient.mix_usages|length %}
                {% if usage_count > 0 %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تنبيه:</strong> هذه المادة مستخدمة في {{ usage_count }} خلطة/خلطات.
                        تعديل القيم الغذائية سيؤثر على حسابات الخلطات.
                    </div>
                    
                    <h6>الخلطات التي تستخدم هذه المادة:</h6>
                    <ul class="list-group">
                        {% for mix_ingredient in ingredient.mix_usages %}
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <a href="{{ url_for('view_feed_mix', mix_id=mix_ingredient.feed_mix.id) }}">
                                {{ mix_ingredient.feed_mix.name }}
                            </a>
                            <span class="badge bg-primary">{{ "%.1f"|format(mix_ingredient.percentage) }}%</span>
                        </li>
                        {% endfor %}
                    </ul>
                {% else %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        هذه المادة غير مستخدمة في أي خلطة حالياً.
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
