<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة الأبقار{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
            color: #2c5530 !important;
        }
        .sidebar {
            background-color: #2c5530;
            min-height: 100vh;
            padding-top: 20px;
        }
        .sidebar .nav-link {
            color: #ffffff;
            padding: 12px 20px;
            margin: 5px 0;
            border-radius: 5px;
        }
        .sidebar .nav-link:hover {
            background-color: #3d7043;
            color: #ffffff;
        }
        .sidebar .nav-link.active {
            background-color: #4a8c57;
            color: #ffffff;
        }
        .main-content {
            padding: 20px;
        }
        .card {
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #2c5530;
            color: white;
            font-weight: bold;
        }
        .btn-primary {
            background-color: #2c5530;
            border-color: #2c5530;
        }
        .btn-primary:hover {
            background-color: #3d7043;
            border-color: #3d7043;
        }
        .stats-card {
            background: linear-gradient(135deg, #2c5530, #4a8c57);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
        }
        .currency {
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background-color: #2c5530;">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-cow"></i> نظام إدارة الأبقار
            </a>

            <!-- شريط البحث -->
            <div class="navbar-nav mx-auto">
                <form class="d-flex" action="{{ url_for('search') }}" method="GET">
                    <input class="form-control me-2" type="search" name="q" placeholder="بحث سريع..." style="width: 300px;">
                    <button class="btn btn-outline-light" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>

            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white" id="current-date">
                    <i class="fas fa-calendar"></i> <span id="date-display"></span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <div class="col-md-2 sidebar">
                <nav class="nav flex-column">
                    <a class="nav-link" href="{{ url_for('index') }}">
                        <i class="fas fa-home"></i> الرئيسية
                    </a>
                    <a class="nav-link" href="{{ url_for('cattle_list') }}">
                        <i class="fas fa-cow"></i> إدارة الأبقار
                    </a>
                    <a class="nav-link" href="{{ url_for('health_records') }}">
                        <i class="fas fa-heartbeat"></i> السجلات الصحية
                    </a>
                    <a class="nav-link" href="{{ url_for('milk_production') }}">
                        <i class="fas fa-tint"></i> إنتاج الحليب
                    </a>
                    <a class="nav-link" href="{{ url_for('breeding_records') }}">
                        <i class="fas fa-baby"></i> التكاثر
                    </a>
                    <a class="nav-link" href="{{ url_for('feed_mixes') }}">
                        <i class="fas fa-seedling"></i> الخلطات العلفية
                    </a>
                    <a class="nav-link" href="{{ url_for('financial_reports') }}">
                        <i class="fas fa-chart-line"></i> التقارير المالية
                    </a>
                    <a class="nav-link" href="{{ url_for('saved_reports') }}">
                        <i class="fas fa-archive"></i> التقارير المحفوظة
                    </a>
                    <a class="nav-link" href="{{ url_for('performance_report') }}">
                        <i class="fas fa-chart-line"></i> تقرير الأداء
                    </a>
                    <a class="nav-link" href="{{ url_for('advanced_dashboard') }}">
                        <i class="fas fa-tachometer-alt"></i> لوحة متقدمة
                    </a>
                    <a class="nav-link" href="{{ url_for('settings') }}">
                        <i class="fas fa-cog"></i> الإعدادات
                    </a>
                </nav>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-10 main-content">
                <!-- رسائل التنبيه -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/ar.min.js"></script>
    <script>
        moment.locale('ar');

        // تحديث التاريخ والوقت
        function updateDateTime() {
            const now = new Date();
            const dateStr = now.toLocaleDateString('ar-JO');
            const timeStr = now.toLocaleTimeString('ar-JO');
            document.getElementById('date-display').textContent = dateStr + ' ' + timeStr;
        }

        // تحديث التاريخ كل ثانية
        setInterval(updateDateTime, 1000);
        updateDateTime(); // تحديث فوري

        function showComingSoon() {
            alert('هذه الميزة قيد التطوير وستكون متاحة قريباً!');
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-JO', {
                style: 'currency',
                currency: 'JOD',
                minimumFractionDigits: 2
            }).format(amount);
        }
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
