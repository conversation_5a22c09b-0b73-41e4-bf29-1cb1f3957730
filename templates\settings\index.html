{% extends "base.html" %}

{% block title %}الإعدادات - نظام إدارة الأبقار{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-cog"></i> إعدادات النظام
        </h1>
    </div>
</div>

<!-- إعدادات عامة -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-sliders-h"></i> الإعدادات العامة
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('save_settings') }}">
                    <div class="mb-3">
                        <label for="farm_name" class="form-label">اسم المزرعة</label>
                        <input type="text" class="form-control" id="farm_name" name="farm_name" value="{{ farm_name }}">
                    </div>

                    <div class="mb-3">
                        <label for="owner_name" class="form-label">اسم المالك</label>
                        <input type="text" class="form-control" id="owner_name" name="owner_name" value="{{ owner_name }}">
                    </div>

                    <div class="mb-3">
                        <label for="location" class="form-label">الموقع</label>
                        <input type="text" class="form-control" id="location" name="location" value="{{ location }}">
                    </div>

                    <div class="mb-3">
                        <label for="milk_price" class="form-label">سعر لتر الحليب (د.أ)</label>
                        <input type="number" step="0.01" class="form-control" id="milk_price" name="milk_price" value="{{ milk_price }}">
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ الإعدادات
                    </button>

                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i> سيتم حفظ الإعدادات في قاعدة البيانات
                        </small>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bell"></i> إعدادات التنبيهات
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('save_settings') }}">
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="vaccination_alerts" name="vaccination_alerts" {{ 'checked' if vaccination_alerts }}>
                            <label class="form-check-label" for="vaccination_alerts">
                                تنبيهات التطعيمات
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="health_alerts" name="health_alerts" {{ 'checked' if health_alerts }}>
                            <label class="form-check-label" for="health_alerts">
                                تنبيهات الفحوصات الطبية
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="breeding_alerts" name="breeding_alerts" {{ 'checked' if breeding_alerts }}>
                            <label class="form-check-label" for="breeding_alerts">
                                تنبيهات الولادة
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="alert_days" class="form-label">عدد أيام التنبيه المسبق</label>
                        <select class="form-select" id="alert_days" name="alert_days">
                            <option value="3" {{ 'selected' if alert_days == '3' }}>3 أيام</option>
                            <option value="7" {{ 'selected' if alert_days == '7' }}>7 أيام</option>
                            <option value="14" {{ 'selected' if alert_days == '14' }}>14 يوم</option>
                            <option value="30" {{ 'selected' if alert_days == '30' }}>30 يوم</option>
                        </select>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التنبيهات
                    </button>

                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-bell"></i> سيتم تطبيق إعدادات التنبيهات على جميع السجلات
                        </small>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- إدارة البيانات -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-database"></i> إدارة البيانات
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('backup_data') }}" class="btn btn-info">
                        <i class="fas fa-download"></i> نسخة احتياطية
                    </a>
                    <a href="{{ url_for('restore_data') }}" class="btn btn-warning">
                        <i class="fas fa-upload"></i> استعادة نسخة احتياطية
                    </a>
                    <a href="{{ url_for('export_data') }}" class="btn btn-success">
                        <i class="fas fa-file-export"></i> تصدير البيانات
                    </a>
                    <button class="btn btn-danger" onclick="confirmDataReset()">
                        <i class="fas fa-trash"></i> إعادة تعيين البيانات
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> معلومات النظام
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>إصدار النظام:</strong></td>
                        <td>1.0.0</td>
                    </tr>
                    <tr>
                        <td><strong>قاعدة البيانات:</strong></td>
                        <td>SQLite</td>
                    </tr>
                    <tr>
                        <td><strong>آخر نسخة احتياطية:</strong></td>
                        <td>لم يتم إنشاء نسخة بعد</td>
                    </tr>
                    <tr>
                        <td><strong>حجم قاعدة البيانات:</strong></td>
                        <td id="db-size">جاري الحساب...</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function confirmDataReset() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟\nسيتم حذف جميع الأبقار والسجلات!')) {
            if (confirm('هذا الإجراء لا يمكن التراجع عنه!\nهل تريد المتابعة؟')) {
                alert('ميزة إعادة التعيين قيد التطوير!');
            }
        }
    }
    
    // حساب حجم قاعدة البيانات (تقديري)
    document.getElementById('db-size').textContent = 'أقل من 1 ميجابايت';
</script>
{% endblock %}
