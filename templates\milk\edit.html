{% extends "base.html" %}

{% block title %}تعديل سجل الحليب - {{ record.cattle.tag_number }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-edit"></i> تعديل سجل إنتاج الحليب للبقرة: {{ record.cattle.tag_number }}</h1>
            <a href="{{ url_for('cattle_detail', id=record.cattle.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> العودة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-tint"></i> تعديل بيانات إنتاج الحليب
            </div>
            <div class="card-body">
                <form method="POST" id="milkForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="date" class="form-label">التاريخ *</label>
                            <input type="date" class="form-control" id="date" name="date" value="{{ record.date.strftime('%Y-%m-%d') if record.date }}" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="morning_amount" class="form-label">كمية الصباح (لتر)</label>
                            <input type="number" step="0.1" class="form-control" id="morning_amount" name="morning_amount" value="{{ record.morning_amount or '' }}" onchange="calculateTotal()">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="evening_amount" class="form-label">كمية المساء (لتر)</label>
                            <input type="number" step="0.1" class="form-control" id="evening_amount" name="evening_amount" value="{{ record.evening_amount or '' }}" onchange="calculateTotal()">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="total_display" class="form-label">الإجمالي (لتر)</label>
                            <input type="text" class="form-control" id="total_display" value="{{ record.total_amount or 0 }}" readonly>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="estimated_value" class="form-label">القيمة المقدرة (د.أ)</label>
                            <input type="text" class="form-control" id="estimated_value" value="{{ '%.2f'|format((record.total_amount or 0) * milk_price) }}" readonly>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="quality_grade" class="form-label">درجة الجودة</label>
                            <select class="form-select" id="quality_grade" name="quality_grade">
                                <option value="">اختر درجة الجودة</option>
                                <option value="ممتاز" {{ 'selected' if record.quality_grade == 'ممتاز' }}>ممتاز</option>
                                <option value="جيد" {{ 'selected' if record.quality_grade == 'جيد' }}>جيد</option>
                                <option value="مقبول" {{ 'selected' if record.quality_grade == 'مقبول' }}>مقبول</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3">{{ record.notes or '' }}</textarea>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('cattle_detail', id=record.cattle.id) }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ التعديلات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // سعر اللتر الحالي من الإعدادات
    const MILK_PRICE_PER_LITER = {{ milk_price }};
    
    function calculateTotal() {
        const morning = parseFloat(document.getElementById('morning_amount').value) || 0;
        const evening = parseFloat(document.getElementById('evening_amount').value) || 0;
        const total = morning + evening;
        const estimatedValue = total * MILK_PRICE_PER_LITER;
        
        document.getElementById('total_display').value = total.toFixed(1);
        document.getElementById('estimated_value').value = estimatedValue.toFixed(2);
    }
</script>
{% endblock %}
